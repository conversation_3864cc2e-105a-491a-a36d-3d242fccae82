<?php
/**
 * Diagnóstico Avançado de Conectividade Pi-hole
 * Este script ajuda a identificar problemas de conexão
 */

require_once 'config/config.php';

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico Pi-hole - Resolução de Problemas</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            border-radius: 1rem;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .success { color: #10b981; background: #ecfdf5; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #10b981; }
        .error { color: #ef4444; background: #fef2f2; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #ef4444; }
        .warning { color: #d97706; background: #fef3c7; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #d97706; }
        .info { color: #3b82f6; background: #eff6ff; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #3b82f6; }
        .test-title { font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem; color: #1e3a8a; }
        pre { background: #f3f4f6; padding: 1rem; border-radius: 0.5rem; overflow-x: auto; font-size: 0.9rem; }
        .solution { background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 0.5rem; padding: 1rem; margin: 1rem 0; }
        .solution h4 { color: #0ea5e9; margin-bottom: 0.5rem; }
        .cmd { background: #1f2937; color: #f9fafb; padding: 0.5rem; border-radius: 0.25rem; font-family: monospace; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Diagnóstico Pi-hole</h1>
        <p>Identificando problemas de conectividade</p>
    </div>

    <?php
    echo '<div class="test-section">';
    echo '<div class="test-title">📋 Configuração Atual</div>';
    echo '<pre>';
    echo 'IP do Pi-hole: ' . PIHOLE_IP . "\n";
    echo 'Porta: ' . PIHOLE_PORT . "\n";
    echo 'URL da API: ' . PIHOLE_API_URL . "\n";
    echo 'Token configurado: ' . (defined('PIHOLE_API_TOKEN') && PIHOLE_API_TOKEN ? 'Sim' : 'Não') . "\n";
    echo '</pre>';
    echo '</div>';

    // Teste 1: Ping básico
    echo '<div class="test-section">';
    echo '<div class="test-title">🌐 Teste 1: Conectividade de Rede (Ping)</div>';
    
    $ping_output = [];
    $ping_result = 0;
    
    // Tentar ping no Windows e Linux
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        exec('ping -n 4 ' . PIHOLE_IP, $ping_output, $ping_result);
    } else {
        exec('ping -c 4 ' . PIHOLE_IP, $ping_output, $ping_result);
    }
    
    if ($ping_result === 0) {
        echo '<div class="success">✅ Ping bem-sucedido para ' . PIHOLE_IP . '</div>';
        echo '<pre>' . implode("\n", array_slice($ping_output, -4)) . '</pre>';
    } else {
        echo '<div class="error">❌ Ping falhou para ' . PIHOLE_IP . '</div>';
        echo '<pre>' . implode("\n", $ping_output) . '</pre>';
        
        echo '<div class="solution">';
        echo '<h4>💡 Possíveis Soluções:</h4>';
        echo '<ul>';
        echo '<li>Verifique se o IP está correto</li>';
        echo '<li>Confirme se o Pi-hole está ligado</li>';
        echo '<li>Verifique a conectividade de rede</li>';
        echo '<li>Teste com outro IP na mesma rede</li>';
        echo '</ul>';
        echo '</div>';
    }
    echo '</div>';

    // Teste 2: Porta específica
    echo '<div class="test-section">';
    echo '<div class="test-title">🔌 Teste 2: Conectividade da Porta ' . PIHOLE_PORT . '</div>';
    
    $socket = @fsockopen(PIHOLE_IP, PIHOLE_PORT, $errno, $errstr, 10);
    if ($socket) {
        echo '<div class="success">✅ Porta ' . PIHOLE_PORT . ' está acessível</div>';
        fclose($socket);
    } else {
        echo '<div class="error">❌ Não foi possível conectar na porta ' . PIHOLE_PORT . '</div>';
        echo '<div class="error">Erro: ' . $errstr . ' (Código: ' . $errno . ')</div>';
        
        echo '<div class="solution">';
        echo '<h4>💡 Possíveis Soluções:</h4>';
        echo '<ul>';
        echo '<li>Verifique se o Pi-hole está rodando na porta 80</li>';
        echo '<li>Teste outras portas comuns: 8080, 8081, 443</li>';
        echo '<li>Verifique firewall no Pi-hole</li>';
        echo '<li>Confirme se o serviço lighttpd está ativo</li>';
        echo '</ul>';
        echo '<div class="cmd">sudo systemctl status lighttpd</div>';
        echo '</div>';
    }
    echo '</div>';

    // Teste 3: Teste HTTP direto
    echo '<div class="test-section">';
    echo '<div class="test-title">🌍 Teste 3: Requisição HTTP</div>';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => 'User-Agent: PiholeDashboard-Diagnostic/1.0'
        ]
    ]);
    
    $test_url = 'http://' . PIHOLE_IP . ':' . PIHOLE_PORT . '/admin/';
    $response = @file_get_contents($test_url, false, $context);
    
    if ($response !== false) {
        echo '<div class="success">✅ Interface web do Pi-hole está acessível</div>';
        echo '<div class="info">URL testada: ' . $test_url . '</div>';
        echo '<div class="info">Tamanho da resposta: ' . strlen($response) . ' bytes</div>';
    } else {
        echo '<div class="error">❌ Interface web não está acessível</div>';
        echo '<div class="error">URL testada: ' . $test_url . '</div>';
        
        // Testar URLs alternativas
        $alt_urls = [
            'http://' . PIHOLE_IP . '/admin/',
            'http://' . PIHOLE_IP . ':8080/admin/',
            'http://' . PIHOLE_IP . ':8081/admin/',
            'https://' . PIHOLE_IP . '/admin/'
        ];
        
        echo '<div class="warning">Testando URLs alternativas...</div>';
        foreach ($alt_urls as $alt_url) {
            $alt_response = @file_get_contents($alt_url, false, $context);
            if ($alt_response !== false) {
                echo '<div class="success">✅ Encontrado em: ' . $alt_url . '</div>';
                echo '<div class="solution">';
                echo '<h4>💡 Solução Encontrada:</h4>';
                echo '<p>Atualize o config/config.php com a porta correta:</p>';
                $port = parse_url($alt_url, PHP_URL_PORT) ?: (parse_url($alt_url, PHP_URL_SCHEME) === 'https' ? 443 : 80);
                echo '<div class="cmd">define(\'PIHOLE_PORT\', \'' . $port . '\');</div>';
                echo '</div>';
                break;
            }
        }
    }
    echo '</div>';

    // Teste 4: API específica
    echo '<div class="test-section">';
    echo '<div class="test-title">🔗 Teste 4: API do Pi-hole</div>';
    
    $api_url = PIHOLE_API_URL . '?summary';
    $api_response = @file_get_contents($api_url, false, $context);
    
    if ($api_response !== false) {
        $api_data = json_decode($api_response, true);
        if ($api_data && json_last_error() === JSON_ERROR_NONE) {
            echo '<div class="success">✅ API do Pi-hole está funcionando</div>';
            echo '<div class="info">URL da API: ' . $api_url . '</div>';
            echo '<pre>' . json_encode($api_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        } else {
            echo '<div class="error">❌ API retornou dados inválidos</div>';
            echo '<div class="error">Resposta: ' . substr($api_response, 0, 200) . '...</div>';
        }
    } else {
        echo '<div class="error">❌ API do Pi-hole não está acessível</div>';
        echo '<div class="error">URL testada: ' . $api_url . '</div>';
        
        echo '<div class="solution">';
        echo '<h4>💡 Possíveis Soluções:</h4>';
        echo '<ul>';
        echo '<li>Verifique se a API está habilitada no Pi-hole</li>';
        echo '<li>Confirme se o caminho /admin/api.php existe</li>';
        echo '<li>Verifique permissões do arquivo</li>';
        echo '<li>Teste acessar a interface web manualmente</li>';
        echo '</ul>';
        echo '</div>';
    }
    echo '</div>';

    // Teste 5: Verificar outras portas comuns
    echo '<div class="test-section">';
    echo '<div class="test-title">🔍 Teste 5: Scan de Portas Comuns</div>';
    
    $common_ports = [80, 8080, 8081, 443, 53, 4711];
    $open_ports = [];
    
    foreach ($common_ports as $port) {
        $socket = @fsockopen(PIHOLE_IP, $port, $errno, $errstr, 3);
        if ($socket) {
            $open_ports[] = $port;
            fclose($socket);
        }
    }
    
    if (!empty($open_ports)) {
        echo '<div class="success">✅ Portas abertas encontradas: ' . implode(', ', $open_ports) . '</div>';
        
        if (!in_array(PIHOLE_PORT, $open_ports)) {
            echo '<div class="warning">⚠️ A porta configurada (' . PIHOLE_PORT . ') não está na lista de portas abertas</div>';
            echo '<div class="solution">';
            echo '<h4>💡 Solução:</h4>';
            echo '<p>Tente uma das portas abertas encontradas. Atualize config/config.php:</p>';
            foreach ($open_ports as $port) {
                if (in_array($port, [80, 8080, 8081, 443])) {
                    echo '<div class="cmd">define(\'PIHOLE_PORT\', \'' . $port . '\');</div>';
                }
            }
            echo '</div>';
        }
    } else {
        echo '<div class="error">❌ Nenhuma porta comum encontrada aberta</div>';
        echo '<div class="solution">';
        echo '<h4>💡 Verificações no Pi-hole:</h4>';
        echo '<ul>';
        echo '<li>Confirme se o Pi-hole está rodando:</li>';
        echo '<div class="cmd">sudo systemctl status pihole-FTL</div>';
        echo '<li>Verifique o serviço web:</li>';
        echo '<div class="cmd">sudo systemctl status lighttpd</div>';
        echo '<li>Reinicie os serviços se necessário:</li>';
        echo '<div class="cmd">sudo systemctl restart lighttpd</div>';
        echo '<div class="cmd">sudo systemctl restart pihole-FTL</div>';
        echo '</ul>';
        echo '</div>';
    }
    echo '</div>';

    // Teste 6: Informações de rede
    echo '<div class="test-section">';
    echo '<div class="test-title">🌐 Teste 6: Informações de Rede</div>';
    
    echo '<div class="info">Servidor atual: ' . $_SERVER['SERVER_NAME'] . '</div>';
    echo '<div class="info">IP do servidor: ' . $_SERVER['SERVER_ADDR'] . '</div>';
    
    // Tentar descobrir a rede
    $server_ip = $_SERVER['SERVER_ADDR'];
    $pihole_ip = PIHOLE_IP;
    
    $server_parts = explode('.', $server_ip);
    $pihole_parts = explode('.', $pihole_ip);
    
    if (count($server_parts) === 4 && count($pihole_parts) === 4) {
        $same_network = ($server_parts[0] === $pihole_parts[0] && 
                        $server_parts[1] === $pihole_parts[1] && 
                        $server_parts[2] === $pihole_parts[2]);
        
        if ($same_network) {
            echo '<div class="success">✅ Servidor e Pi-hole estão na mesma rede</div>';
        } else {
            echo '<div class="warning">⚠️ Servidor e Pi-hole podem estar em redes diferentes</div>';
            echo '<div class="info">Rede do servidor: ' . $server_parts[0] . '.' . $server_parts[1] . '.' . $server_parts[2] . '.x</div>';
            echo '<div class="info">Rede do Pi-hole: ' . $pihole_parts[0] . '.' . $pihole_parts[1] . '.' . $pihole_parts[2] . '.x</div>';
        }
    }
    echo '</div>';
    ?>

    <div class="test-section">
        <div class="test-title">🛠️ Próximos Passos</div>
        
        <div class="solution">
            <h4>Se o Pi-hole estiver inacessível:</h4>
            <ol>
                <li><strong>Verifique o IP:</strong> Confirme o IP correto do Pi-hole na sua rede</li>
                <li><strong>Teste manual:</strong> Acesse http://************/admin no navegador</li>
                <li><strong>Verifique serviços:</strong> SSH no Pi-hole e execute os comandos de diagnóstico</li>
                <li><strong>Firewall:</strong> Verifique se não há bloqueios de firewall</li>
                <li><strong>Rede:</strong> Confirme se ambos estão na mesma rede</li>
            </ol>
        </div>

        <div class="solution">
            <h4>Comandos úteis no Pi-hole (via SSH):</h4>
            <div class="cmd">pihole status</div>
            <div class="cmd">sudo systemctl status pihole-FTL</div>
            <div class="cmd">sudo systemctl status lighttpd</div>
            <div class="cmd">sudo netstat -tlnp | grep :80</div>
            <div class="cmd">sudo pihole restartdns</div>
        </div>

        <div class="info">
            <strong>💡 Dica:</strong> Se encontrar o Pi-hole em uma porta diferente, 
            atualize o arquivo config/config.php com a porta correta.
        </div>
    </div>

    <script>
        // Auto-refresh a cada 30 segundos
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>
