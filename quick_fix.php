<?php
/**
 * Script de Correção Rápida para Pi-hole
 * Testa configurações comuns e aplica a correção automaticamente
 */

require_once 'config/config.php';

header('Content-Type: text/html; charset=UTF-8');

// Função para testar uma configuração específica
function testPiholeConfig($ip, $port) {
    // Teste 1: Conectividade da porta
    $socket = @fsockopen($ip, $port, $errno, $errstr, 5);
    if (!$socket) {
        return [
            'success' => false,
            'step' => 'port',
            'error' => "Porta {$port} não acessível: {$errstr}"
        ];
    }
    fclose($socket);
    
    // Teste 2: Interface web
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $web_url = "http://{$ip}:{$port}/admin/";
    $web_response = @file_get_contents($web_url, false, $context);
    
    if ($web_response === false) {
        return [
            'success' => false,
            'step' => 'web',
            'error' => "Interface web não acessível em {$web_url}"
        ];
    }
    
    // Teste 3: API
    $api_url = "http://{$ip}:{$port}/admin/api.php?summary";
    $api_response = @file_get_contents($api_url, false, $context);
    
    if ($api_response === false) {
        return [
            'success' => false,
            'step' => 'api',
            'error' => "API não acessível em {$api_url}"
        ];
    }
    
    $api_data = json_decode($api_response, true);
    if (!$api_data || !isset($api_data['dns_queries_today'])) {
        return [
            'success' => false,
            'step' => 'api_data',
            'error' => "API não retornou dados válidos do Pi-hole"
        ];
    }
    
    return [
        'success' => true,
        'ip' => $ip,
        'port' => $port,
        'api_url' => $api_url,
        'data' => $api_data
    ];
}

// Configurações para testar
$test_configs = [
    // IP atual com portas diferentes
    [PIHOLE_IP, 80],
    [PIHOLE_IP, 8080],
    [PIHOLE_IP, 8081],
    [PIHOLE_IP, 443],
    [PIHOLE_IP, 4711],
    
    // IPs comuns com porta 80
    ['***********', 80],
    ['***********00', 80],
    ['***********01', 80],
    ['***********02', 80],
    ['*************', 80],
    ['***********', 80],
    ['*************', 80],
    ['********', 80],
    ['**********', 80],
    
    // Sem porta específica (padrão do Pi-hole)
    [PIHOLE_IP, 80],
];

$working_config = null;
$test_results = [];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Correção Rápida - Pi-hole</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border-radius: 1rem;
        }
        .section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .success { background: #ecfdf5; border-left: 4px solid #10b981; color: #065f46; padding: 1rem; margin: 0.5rem 0; border-radius: 0.5rem; }
        .error { background: #fef2f2; border-left: 4px solid #ef4444; color: #991b1b; padding: 1rem; margin: 0.5rem 0; border-radius: 0.5rem; }
        .warning { background: #fef3c7; border-left: 4px solid #d97706; color: #92400e; padding: 1rem; margin: 0.5rem 0; border-radius: 0.5rem; }
        .info { background: #eff6ff; border-left: 4px solid #3b82f6; color: #1e40af; padding: 1rem; margin: 0.5rem 0; border-radius: 0.5rem; }
        .config-box {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        .btn:hover { background: #2563eb; }
        .btn-success { background: #10b981; }
        .btn-success:hover { background: #059669; }
        pre { background: #f3f4f6; padding: 1rem; border-radius: 0.5rem; overflow-x: auto; font-size: 0.9rem; }
        .testing { color: #d97706; font-weight: bold; }
        .step { margin: 0.5rem 0; padding: 0.5rem; background: #f8fafc; border-radius: 0.25rem; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Correção Rápida Pi-hole</h1>
        <p>Testando configurações automaticamente...</p>
    </div>

    <div class="section">
        <h3>📋 Configuração Atual</h3>
        <div class="info">
            <strong>IP:</strong> <?php echo PIHOLE_IP; ?><br>
            <strong>Porta:</strong> <?php echo PIHOLE_PORT; ?><br>
            <strong>URL da API:</strong> <?php echo PIHOLE_API_URL; ?>
        </div>
    </div>

    <div class="section">
        <h3>🔍 Testando Configurações...</h3>
        
        <?php
        foreach ($test_configs as $index => $config) {
            list($ip, $port) = $config;
            
            echo "<div class='step'>";
            echo "<span class='testing'>Testando {$ip}:{$port}...</span>";
            
            $result = testPiholeConfig($ip, $port);
            $test_results[] = $result;
            
            if ($result['success']) {
                echo "<div class='success'>";
                echo "<h4>✅ Pi-hole encontrado!</h4>";
                echo "<p><strong>IP:</strong> {$result['ip']}</p>";
                echo "<p><strong>Porta:</strong> {$result['port']}</p>";
                echo "<p><strong>URL da API:</strong> {$result['api_url']}</p>";
                echo "<p><strong>Status:</strong> {$result['data']['status']}</p>";
                echo "<p><strong>Queries hoje:</strong> " . number_format($result['data']['dns_queries_today']) . "</p>";
                echo "<p><strong>Bloqueadas hoje:</strong> " . number_format($result['data']['ads_blocked_today']) . "</p>";
                echo "</div>";
                
                $working_config = $result;
                break;
            } else {
                echo "<span style='color: #ef4444;'> ❌ {$result['error']}</span>";
            }
            
            echo "</div>";
            
            // Flush output para mostrar progresso
            if (ob_get_level()) ob_flush();
            flush();
        }
        ?>
    </div>

    <?php if ($working_config): ?>
        <div class="section">
            <h3>🎉 Solução Encontrada!</h3>
            
            <div class="success">
                <h4>Pi-hole está funcionando em:</h4>
                <p><strong>IP:</strong> <?php echo $working_config['ip']; ?></p>
                <p><strong>Porta:</strong> <?php echo $working_config['port']; ?></p>
                <p><strong>URL da API:</strong> <?php echo $working_config['api_url']; ?></p>
            </div>

            <h4>📝 Atualize sua configuração:</h4>
            <p>Copie e cole no arquivo <code>config/config.php</code>:</p>
            
            <div class="config-box">
define('PIHOLE_IP', '<?php echo $working_config['ip']; ?>');
define('PIHOLE_PORT', '<?php echo $working_config['port']; ?>');
define('PIHOLE_API_URL', '<?php echo $working_config['api_url']; ?>');
            </div>

            <h4>📊 Dados atuais do Pi-hole:</h4>
            <pre><?php echo json_encode($working_config['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></pre>

            <div class="info">
                <h4>🚀 Próximos passos:</h4>
                <ol>
                    <li>Atualize o arquivo <code>config/config.php</code> com as configurações acima</li>
                    <li>Teste novamente: <a href="test_connection.php" class="btn">🔍 Testar Conectividade</a></li>
                    <li>Acesse o dashboard: <a href="index.php" class="btn btn-success">🏠 Dashboard</a></li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h3>🔧 Aplicar Correção Automaticamente</h3>
            <div class="warning">
                <p><strong>⚠️ Atenção:</strong> Isso irá sobrescrever suas configurações atuais!</p>
            </div>
            
            <form method="POST" style="text-align: center;">
                <input type="hidden" name="new_ip" value="<?php echo $working_config['ip']; ?>">
                <input type="hidden" name="new_port" value="<?php echo $working_config['port']; ?>">
                <button type="submit" name="apply_fix" class="btn btn-success" 
                        onclick="return confirm('Tem certeza que deseja aplicar esta correção?')">
                    ✅ Aplicar Correção Automaticamente
                </button>
            </form>
        </div>

    <?php else: ?>
        <div class="section">
            <h3>❌ Nenhuma Configuração Funcionando</h3>
            
            <div class="error">
                <h4>Pi-hole não foi encontrado automaticamente</h4>
                <p>Nenhuma das configurações testadas funcionou.</p>
            </div>

            <div class="warning">
                <h4>🔍 Verificações manuais necessárias:</h4>
                <ol>
                    <li><strong>Confirme se o Pi-hole está ligado e funcionando</strong></li>
                    <li><strong>Verifique o IP correto:</strong>
                        <ul>
                            <li>Acesse o roteador e veja dispositivos conectados</li>
                            <li>No Pi-hole, execute: <code>hostname -I</code></li>
                            <li>Ou: <code>ip addr show</code></li>
                        </ul>
                    </li>
                    <li><strong>Teste manualmente no navegador:</strong>
                        <ul>
                            <li>Acesse: <code>http://IP_DO_PIHOLE/admin</code></li>
                            <li>Se funcionar, o IP está correto</li>
                        </ul>
                    </li>
                    <li><strong>Verifique serviços no Pi-hole:</strong>
                        <ul>
                            <li><code>sudo systemctl status pihole-FTL</code></li>
                            <li><code>sudo systemctl status lighttpd</code></li>
                        </ul>
                    </li>
                </ol>
            </div>

            <div class="info">
                <h4>🛠️ Ferramentas adicionais:</h4>
                <a href="diagnose_pihole.php" class="btn">🔍 Diagnóstico Completo</a>
                <a href="find_pihole.php" class="btn">🌐 Busca na Rede</a>
                <a href="TROUBLESHOOTING.md" class="btn">📖 Guia Completo</a>
            </div>
        </div>
    <?php endif; ?>

    <?php
    // Aplicar correção se solicitado
    if (isset($_POST['apply_fix']) && isset($_POST['new_ip']) && isset($_POST['new_port'])) {
        $new_ip = $_POST['new_ip'];
        $new_port = $_POST['new_port'];
        $new_api_url = "http://{$new_ip}:{$new_port}/admin/api.php";
        
        $config_content = file_get_contents('config/config.php');
        
        // Substituir configurações
        $config_content = preg_replace(
            "/define\('PIHOLE_IP', '[^']+'\);/", 
            "define('PIHOLE_IP', '{$new_ip}');", 
            $config_content
        );
        
        $config_content = preg_replace(
            "/define\('PIHOLE_PORT', '[^']+'\);/", 
            "define('PIHOLE_PORT', '{$new_port}');", 
            $config_content
        );
        
        $config_content = preg_replace(
            "/define\('PIHOLE_API_URL', '[^']+'\);/", 
            "define('PIHOLE_API_URL', '{$new_api_url}');", 
            $config_content
        );
        
        if (file_put_contents('config/config.php', $config_content)) {
            echo "<div class='section'>";
            echo "<div class='success'>";
            echo "<h4>✅ Configuração aplicada com sucesso!</h4>";
            echo "<p>O arquivo config/config.php foi atualizado.</p>";
            echo "<p><a href='test_connection.php' class='btn btn-success'>🔍 Testar Agora</a></p>";
            echo "</div>";
            echo "</div>";
        } else {
            echo "<div class='section'>";
            echo "<div class='error'>";
            echo "<h4>❌ Erro ao aplicar configuração</h4>";
            echo "<p>Não foi possível escrever no arquivo config/config.php</p>";
            echo "<p>Verifique as permissões do arquivo.</p>";
            echo "</div>";
            echo "</div>";
        }
    }
    ?>
</body>
</html>
