/**
 * Dashboard Zabbix - JavaScript
 * Funcionalidades interativas e atualizações em tempo real
 */

class ZabbixDashboard {
    constructor() {
        this.refreshInterval = 30000; // 30 segundos
        this.charts = {};
        this.hosts = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.addAnimations();
    }

    setupEventListeners() {
        // Botão de refresh manual
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshAll());
        }

        // Refresh de hosts
        const refreshHostsBtn = document.getElementById('refresh-hosts');
        if (refreshHostsBtn) {
            refreshHostsBtn.addEventListener('click', () => this.loadHosts());
        }

        // Filtro de grupos
        const groupFilter = document.getElementById('group-filter');
        if (groupFilter) {
            groupFilter.addEventListener('change', (e) => {
                this.loadHosts(e.target.value);
            });
        }
    }

    async refreshAll() {
        try {
            this.showLoading();
            
            await Promise.all([
                this.loadSummary(),
                this.loadHosts(),
                this.loadProblems(5)
            ]);
            
            this.updateLastUpdate();
            this.showSuccess('Dados atualizados com sucesso!');
        } catch (error) {
            this.showError('Erro ao atualizar dados: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadSummary() {
        try {
            const response = await fetch('api/get_summary.php');
            const data = await response.json();
            
            if (data.success) {
                this.updateSummary(data.data);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Erro ao carregar resumo:', error);
            throw error;
        }
    }

    updateSummary(data) {
        this.updateElement('total-hosts', data.total_hosts);
        this.updateElement('hosts-online', data.hosts_up);
        this.updateElement('critical-problems', data.critical_problems);
        this.updateElement('warning-problems', data.warning_problems);
    }

    async loadHosts(groupId = null) {
        try {
            const url = groupId ? 
                `api/get_hosts.php?group_id=${groupId}&include_metrics=true` : 
                'api/get_hosts.php?include_metrics=true';
                
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.hosts = data.data;
                this.renderHosts(data.data);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Erro ao carregar hosts:', error);
            throw error;
        }
    }

    renderHosts(hosts) {
        const container = document.getElementById('servers-grid');
        if (!container) return;

        if (hosts.length === 0) {
            container.innerHTML = `
                <div class="server-card">
                    <div style="text-align: center; padding: 2rem;">
                        <p>Nenhum servidor encontrado</p>
                    </div>
                </div>
            `;
            return;
        }

        container.innerHTML = hosts.map(host => this.createHostCard(host)).join('');
    }

    createHostCard(host) {
        const metrics = host.metrics || {};
        const cpu = metrics.cpu || { usage: 0, status: 'unknown' };
        const memory = metrics.memory || { usage: 0, status: 'unknown' };
        const disk = metrics.disk || { usage: 0, status: 'unknown' };

        return `
            <div class="server-card fade-in-up" data-hostid="${host.hostid}">
                <div class="server-header">
                    <div class="server-name" title="${host.hostname}">${host.name}</div>
                    <div class="server-status status-${host.status_class}">${host.status_text}</div>
                </div>
                <div class="server-metrics">
                    <div class="metric">
                        <div class="metric-label">CPU</div>
                        <div class="metric-value">${cpu.usage.toFixed(1)}%</div>
                        <div class="metric-bar">
                            <div class="metric-progress cpu" style="width: ${cpu.usage}%"></div>
                        </div>
                        <div class="metric-info">${this.getStatusText(cpu.status)}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Memória</div>
                        <div class="metric-value">${memory.usage.toFixed(1)}%</div>
                        <div class="metric-bar">
                            <div class="metric-progress memory" style="width: ${memory.usage}%"></div>
                        </div>
                        <div class="metric-info">${this.getStatusText(memory.status)}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Disco</div>
                        <div class="metric-value">${disk.usage.toFixed(1)}%</div>
                        <div class="metric-bar">
                            <div class="metric-progress disk" style="width: ${disk.usage}%"></div>
                        </div>
                        <div class="metric-info">${this.getStatusText(disk.status)}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">Rede</div>
                        <div class="metric-value">${host.status_text}</div>
                        <div class="metric-info">${host.interfaces?.[0]?.ip || 'N/A'}</div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadProblems(limit = 10) {
        try {
            const response = await fetch(`api/get_problems.php?limit=${limit}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderProblems(data.data);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Erro ao carregar problemas:', error);
            throw error;
        }
    }

    renderProblems(problems) {
        const tbody = document.getElementById('problems-tbody');
        if (!tbody) return;

        if (problems.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" style="text-align: center; padding: 2rem; color: var(--success-color);">
                        ✅ Nenhum problema ativo
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = problems.map(problem => `
            <tr>
                <td>
                    <span class="severity-${problem.severity_class}">${problem.severity_text}</span>
                </td>
                <td>${problem.name}</td>
                <td>
                    ${problem.hosts.map(host => host.name).join(', ')}
                </td>
                <td title="${problem.time}">${problem.time_ago}</td>
            </tr>
        `).join('');
    }

    getStatusText(status) {
        switch (status) {
            case 'ok': return 'Normal';
            case 'warning': return 'Atenção';
            case 'critical': return 'Crítico';
            default: return 'Desconhecido';
        }
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            element.classList.add('fade-in-up');
            setTimeout(() => element.classList.remove('fade-in-up'), 600);
        }
    }

    updateLastUpdate() {
        const element = document.getElementById('last-update');
        if (element) {
            const now = new Date();
            element.textContent = now.toLocaleString('pt-BR');
        }
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshTimer = setInterval(() => {
            this.refreshAll();
        }, this.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    showLoading() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<span class="loading"></span> Carregando...';
            refreshBtn.disabled = true;
        }
    }

    hideLoading() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '🔄 Atualizar';
            refreshBtn.disabled = false;
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Adicionar estilos inline
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
            background: ${type === 'error' ? 'var(--danger-color)' : 
                        type === 'success' ? 'var(--success-color)' : 
                        'var(--primary-color)'};
        `;
        
        document.body.appendChild(notification);
        
        // Remover após 5 segundos
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    addAnimations() {
        // Adicionar animações CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// Utilitários globais
window.zabbixUtils = {
    formatBytes: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatUptime: function(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) return `${days}d ${hours}h ${minutes}m`;
        if (hours > 0) return `${hours}h ${minutes}m`;
        return `${minutes}m`;
    },
    
    getStatusColor: function(status) {
        switch (status) {
            case 'ok': return '#10b981';
            case 'warning': return '#f59e0b';
            case 'critical': return '#ef4444';
            default: return '#6b7280';
        }
    }
};
