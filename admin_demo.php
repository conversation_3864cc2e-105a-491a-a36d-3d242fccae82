<?php
/**
 * Demonstração das Funcionalidades Administrativas
 * Este arquivo mostra como usar as funções de controle do Pi-hole
 */

require_once 'config/config.php';
require_once 'classes/PiholeAPI.php';

// Verificar se há ação solicitada
$action = $_GET['action'] ?? null;
$message = '';
$messageType = '';

if ($action && defined('PIHOLE_API_TOKEN') && PIHOLE_API_TOKEN) {
    $pihole = new PiholeAPI();
    
    switch ($action) {
        case 'enable':
            $result = $pihole->enable();
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
            break;
            
        case 'disable':
            $duration = (int)($_GET['duration'] ?? 0);
            $result = $pihole->disable($duration);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
            break;
            
        case 'status':
            $result = $pihole->getStats();
            if ($result['success']) {
                $message = 'Status atual: ' . $result['data']['status'];
                $messageType = 'info';
            } else {
                $message = 'Erro ao obter status: ' . $result['message'];
                $messageType = 'error';
            }
            break;
    }
}

// Obter status atual
$pihole = new PiholeAPI();
$currentStats = $pihole->getStats();
$currentStatus = $currentStats['success'] ? $currentStats['data']['status'] : 'unknown';
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Controles Administrativos - Dashboard Pi-hole</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            border-radius: 1rem;
        }
        .control-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .status-display {
            text-align: center;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 1rem;
            font-size: 1.2rem;
            font-weight: bold;
        }
        .status-enabled {
            background: #ecfdf5;
            color: #10b981;
            border: 2px solid #10b981;
        }
        .status-disabled {
            background: #fef2f2;
            color: #ef4444;
            border: 2px solid #ef4444;
        }
        .status-unknown {
            background: #f3f4f6;
            color: #6b7280;
            border: 2px solid #6b7280;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.5rem;
            border: none;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-success {
            background: #10b981;
            color: white;
        }
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            font-weight: 600;
        }
        .message.success {
            background: #ecfdf5;
            color: #10b981;
            border-left: 4px solid #10b981;
        }
        .message.error {
            background: #fef2f2;
            color: #ef4444;
            border-left: 4px solid #ef4444;
        }
        .message.info {
            background: #eff6ff;
            color: #3b82f6;
            border-left: 4px solid #3b82f6;
        }
        .warning {
            background: #fef3c7;
            color: #d97706;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #d97706;
            margin: 1rem 0;
        }
        .duration-controls {
            margin: 1rem 0;
        }
        .duration-controls select {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.25rem;
            margin: 0 0.5rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Controles Administrativos</h1>
        <p>Gerenciar Pi-hole remotamente</p>
    </div>

    <?php if (!defined('PIHOLE_API_TOKEN') || !PIHOLE_API_TOKEN): ?>
        <div class="warning">
            ⚠️ <strong>API Token não configurado!</strong><br>
            Para usar as funcionalidades administrativas, configure o PIHOLE_API_TOKEN em config/config.php
        </div>
    <?php endif; ?>

    <?php if ($message): ?>
        <div class="message <?php echo $messageType; ?>">
            <?php echo htmlspecialchars($message); ?>
        </div>
    <?php endif; ?>

    <!-- Status Atual -->
    <div class="status-display status-<?php echo $currentStatus; ?>">
        🛡️ Pi-hole está: <?php echo strtoupper($currentStatus); ?>
    </div>

    <!-- Controles -->
    <div class="control-section">
        <h3>Controles Básicos</h3>
        
        <?php if ($currentStatus === 'enabled'): ?>
            <a href="?action=disable" class="btn btn-danger">
                ⏸️ Desabilitar Pi-hole
            </a>
        <?php else: ?>
            <a href="?action=enable" class="btn btn-success">
                ▶️ Habilitar Pi-hole
            </a>
        <?php endif; ?>
        
        <a href="?action=status" class="btn btn-primary">
            🔄 Verificar Status
        </a>
    </div>

    <!-- Desabilitar Temporariamente -->
    <div class="control-section">
        <h3>Desabilitar Temporariamente</h3>
        <p>Desabilite o Pi-hole por um período específico:</p>
        
        <div class="duration-controls">
            <a href="?action=disable&duration=300" class="btn btn-danger">5 minutos</a>
            <a href="?action=disable&duration=600" class="btn btn-danger">10 minutos</a>
            <a href="?action=disable&duration=1800" class="btn btn-danger">30 minutos</a>
            <a href="?action=disable&duration=3600" class="btn btn-danger">1 hora</a>
        </div>
    </div>

    <!-- Informações -->
    <div class="control-section">
        <h3>ℹ️ Informações</h3>
        <ul>
            <li><strong>IP do Pi-hole:</strong> <?php echo PIHOLE_IP; ?></li>
            <li><strong>API Token:</strong> <?php echo defined('PIHOLE_API_TOKEN') && PIHOLE_API_TOKEN ? 'Configurado' : 'Não configurado'; ?></li>
            <li><strong>Status atual:</strong> <?php echo $currentStatus; ?></li>
            <?php if ($currentStats['success']): ?>
                <li><strong>Queries hoje:</strong> <?php echo number_format($currentStats['data']['dns_queries_today']); ?></li>
                <li><strong>Bloqueadas hoje:</strong> <?php echo number_format($currentStats['data']['ads_blocked_today']); ?></li>
                <li><strong>% Bloqueadas:</strong> <?php echo $currentStats['data']['ads_percentage_today']; ?>%</li>
            <?php endif; ?>
        </ul>
    </div>

    <!-- Navegação -->
    <div class="control-section">
        <h3>🧭 Navegação</h3>
        <a href="index.php" class="btn btn-primary">🏠 Dashboard Principal</a>
        <a href="test_connection.php" class="btn btn-primary">🔍 Teste de Conectividade</a>
    </div>

    <!-- Aviso de Segurança -->
    <div class="warning">
        <strong>⚠️ Aviso de Segurança:</strong><br>
        Este painel permite controlar o Pi-hole remotamente. Use com cuidado e mantenha o acesso restrito.
        Em produção, considere implementar autenticação adicional.
    </div>

    <script>
        // Auto-refresh da página a cada 30 segundos para mostrar status atualizado
        setTimeout(function() {
            if (!window.location.search.includes('action=')) {
                window.location.href = window.location.pathname + '?action=status';
            }
        }, 30000);

        // Confirmação para ações críticas
        document.querySelectorAll('.btn-danger').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                if (!confirm('Tem certeza que deseja executar esta ação?')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
