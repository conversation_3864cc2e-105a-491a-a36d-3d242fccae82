# 🔧 Guia de Solução de Problemas - Dashboard Pi-hole

## ❌ Erro: "Erro ao conectar com o Pi-hole em ************"

Este erro indica que o dashboard não consegue se comunicar com o Pi-hole. Siga os passos abaixo para resolver:

### 🔍 Passo 1: Diagnóstico Automático

Execute os scripts de diagnóstico incluídos:

1. **Diagnóstico Completo:**
   ```
   http://seu-servidor/diagnose_pihole.php
   ```

2. **Busca Automática:**
   ```
   http://seu-servidor/find_pihole.php
   ```

3. **Teste de Conectividade:**
   ```
   http://seu-servidor/test_connection.php
   ```

### 🌐 Passo 2: Verificar Conectividade de Rede

#### No Windows:
```cmd
ping ************
telnet ************ 80
```

#### No Linux/Mac:
```bash
ping -c 4 ************
nc -zv ************ 80
```

### 🔧 Passo 3: Verificar Pi-hole (via SSH)

Conecte-se ao Pi-hole via SSH e execute:

```bash
# Verificar status do Pi-hole
pihole status

# Verificar serviços
sudo systemctl status pihole-FTL
sudo systemctl status lighttpd

# Verificar portas abertas
sudo netstat -tlnp | grep :80
sudo ss -tlnp | grep :80

# Verificar logs
sudo tail -f /var/log/pihole.log
sudo tail -f /var/log/lighttpd/error.log
```

### 🛠️ Passo 4: Soluções Comuns

#### Problema: IP Incorreto
**Sintoma:** Ping falha
**Solução:**
1. Encontre o IP correto do Pi-hole:
   ```bash
   # No Pi-hole
   hostname -I
   ip addr show
   ```
2. Atualize `config/config.php`:
   ```php
   define('PIHOLE_IP', 'IP_CORRETO_AQUI');
   ```

#### Problema: Porta Incorreta
**Sintoma:** Ping funciona, mas conexão HTTP falha
**Solução:**
1. Verifique qual porta o lighttpd está usando:
   ```bash
   sudo netstat -tlnp | grep lighttpd
   ```
2. Portas comuns: 80, 8080, 8081, 443
3. Atualize `config/config.php`:
   ```php
   define('PIHOLE_PORT', 'PORTA_CORRETA');
   ```

#### Problema: Firewall Bloqueando
**Sintoma:** Timeout na conexão
**Solução:**
1. No Pi-hole, verifique firewall:
   ```bash
   sudo ufw status
   sudo iptables -L
   ```
2. Libere a porta se necessário:
   ```bash
   sudo ufw allow 80
   sudo ufw allow from SEU_IP_SERVIDOR
   ```

#### Problema: Serviço Parado
**Sintoma:** Porta não responde
**Solução:**
1. Reiniciar serviços:
   ```bash
   sudo systemctl restart lighttpd
   sudo systemctl restart pihole-FTL
   ```
2. Verificar se iniciaram:
   ```bash
   sudo systemctl status lighttpd
   sudo systemctl status pihole-FTL
   ```

#### Problema: Configuração do lighttpd
**Sintoma:** Erro 404 na API
**Solução:**
1. Verificar configuração:
   ```bash
   sudo nano /etc/lighttpd/lighttpd.conf
   ```
2. Verificar se o módulo fastcgi está habilitado
3. Reiniciar lighttpd

### 🔄 Passo 5: Teste Manual da API

Teste a API diretamente no navegador:

```
http://************/admin/api.php?summary
```

Deve retornar JSON com estatísticas do Pi-hole.

### 📱 Passo 6: Verificar Rede

#### Mesmo Segmento de Rede?
- Servidor dashboard: `*************`
- Pi-hole: `************`
- **Problema:** Redes diferentes!

#### Soluções:
1. **Mover para mesma rede:** Configure ambos na mesma faixa IP
2. **Configurar roteamento:** Permitir comunicação entre redes
3. **VPN/Túnel:** Se estiverem em locais diferentes

### 🔧 Passo 7: Configurações Avançadas

#### Timeout Aumentado
Se a rede for lenta, aumente o timeout em `classes/PiholeAPI.php`:
```php
$this->timeout = 30; // Aumentar de 10 para 30 segundos
```

#### Debug Habilitado
Habilite logs detalhados em `config/config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '/tmp/pihole_dashboard.log');
```

#### Proxy/Load Balancer
Se usar proxy, configure headers corretos:
```php
// Em classes/PiholeAPI.php, no método makeRequest
'header' => [
    'User-Agent: PiholeDashboard/1.0',
    'Accept: application/json',
    'X-Forwarded-For: ' . $_SERVER['REMOTE_ADDR']
]
```

### 🆘 Passo 8: Casos Específicos

#### Pi-hole em Docker
```bash
# Verificar container
docker ps | grep pihole
docker logs pihole

# Verificar mapeamento de portas
docker port pihole
```

#### Pi-hole com HTTPS
```php
// config/config.php
define('PIHOLE_API_URL', 'https://************/admin/api.php');
```

#### Pi-hole com Autenticação Básica
```php
// Em classes/PiholeAPI.php
$context = stream_context_create([
    'http' => [
        'header' => 'Authorization: Basic ' . base64_encode('user:pass')
    ]
]);
```

### ✅ Verificação Final

Após aplicar as correções:

1. Execute `test_connection.php` novamente
2. Verifique se todos os testes passam
3. Acesse o dashboard principal
4. Confirme se as estatísticas carregam
5. Teste os controles administrativos

### 📞 Ainda com Problemas?

Se ainda não funcionar:

1. **Capture logs detalhados:**
   ```bash
   # No servidor web
   tail -f /var/log/apache2/error.log
   tail -f /var/log/nginx/error.log
   
   # No Pi-hole
   tail -f /var/log/pihole.log
   ```

2. **Teste com curl:**
   ```bash
   curl -v http://************/admin/api.php?summary
   ```

3. **Verifique DNS:**
   ```bash
   nslookup ************
   ```

4. **Teste de outras máquinas na rede**

### 🔗 Links Úteis

- [Documentação oficial Pi-hole](https://docs.pi-hole.net/)
- [Troubleshooting Pi-hole](https://docs.pi-hole.net/main/troubleshooting/)
- [Pi-hole API Documentation](https://discourse.pi-hole.net/t/pi-hole-api/1863)

---

**💡 Dica:** Mantenha este guia salvo para referência futura. A maioria dos problemas são relacionados à rede ou configuração de portas.
