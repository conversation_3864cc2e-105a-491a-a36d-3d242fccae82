<?php
/**
 * Endpoint para testar conexão com Pi-hole via AJAX
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$ip = $_GET['ip'] ?? '';
$port = $_GET['port'] ?? 80;

if (!$ip) {
    echo json_encode(['success' => false, 'error' => 'IP não fornecido']);
    exit;
}

// Validar IP
if (!filter_var($ip, FILTER_VALIDATE_IP)) {
    echo json_encode(['success' => false, 'error' => 'IP inválido']);
    exit;
}

// Testar conectividade da porta
$socket = @fsockopen($ip, $port, $errno, $errstr, 3);
if (!$socket) {
    echo json_encode([
        'success' => false, 
        'error' => "Porta não acessível: $errstr ($errno)",
        'ip' => $ip,
        'port' => $port
    ]);
    exit;
}
fclose($socket);

// Testar se é um Pi-hole
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'method' => 'GET',
        'header' => 'User-Agent: PiholeDashboard-Scanner/1.0'
    ]
]);

$test_url = "http://{$ip}:{$port}/admin/api.php?summary";
$response = @file_get_contents($test_url, false, $context);

if ($response === false) {
    echo json_encode([
        'success' => false, 
        'error' => 'Não foi possível acessar a API',
        'ip' => $ip,
        'port' => $port,
        'url' => $test_url
    ]);
    exit;
}

$data = json_decode($response, true);
if (!$data || json_last_error() !== JSON_ERROR_NONE) {
    echo json_encode([
        'success' => false, 
        'error' => 'Resposta da API inválida',
        'ip' => $ip,
        'port' => $port,
        'response' => substr($response, 0, 200)
    ]);
    exit;
}

// Verificar se é realmente um Pi-hole
if (!isset($data['dns_queries_today'])) {
    echo json_encode([
        'success' => false, 
        'error' => 'Não parece ser um Pi-hole',
        'ip' => $ip,
        'port' => $port,
        'data' => $data
    ]);
    exit;
}

// Sucesso!
echo json_encode([
    'success' => true,
    'ip' => $ip,
    'port' => $port,
    'url' => $test_url,
    'data' => $data,
    'config' => [
        'PIHOLE_IP' => $ip,
        'PIHOLE_PORT' => $port,
        'PIHOLE_API_URL' => "http://{$ip}:{$port}/admin/api.php"
    ]
]);
?>
