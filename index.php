<?php
/**
 * Dashboard Pi-hole - P<PERSON><PERSON>a Principal
 * Design inspirado na Megainffo
 */

require_once 'config/config.php';
require_once 'classes/PiholeAPI.php';

// Inicializar API do Pi-hole
$pihole = new PiholeAPI();

// Obter dados iniciais
$stats = $pihole->getStats();
$systemInfo = $pihole->getSystemInfo();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo DASHBOARD_TITLE; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Dashboard Pi-hole
                <span style="font-size: 0.8rem; opacity: 0.8;">Megainffo Style</span>
            </div>
            <nav class="nav">
                <a href="#" class="active">Dashboard</a>
                <a href="pages/logs.php">Logs</a>
                <a href="pages/domains.php">Domínios</a>
                <a href="pages/clients.php">Clientes</a>
                <a href="pages/settings.php">Configurações</a>
            </nav>
        </div>
    </header>

    <!-- Container Principal -->
    <main class="container">
        <!-- Status e Controles -->
        <div class="status-section mb-3">
            <div class="stat-card">
                <div class="stat-header">
                    <h3>Status do Sistema</h3>
                    <div class="controls">
                        <button id="refresh-btn" class="btn btn-primary">
                            🔄 Atualizar
                        </button>
                        <?php if ($stats['success'] && $stats['data']['status'] === 'enabled'): ?>
                            <button id="disable-btn" class="btn btn-danger">
                                ⏸️ Desabilitar
                            </button>
                        <?php else: ?>
                            <button id="enable-btn" class="btn btn-success">
                                ▶️ Habilitar
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="status-info">
                    <div class="status-item">
                        <span class="status-label">Pi-hole:</span>
                        <span id="pihole-status" class="<?php echo $stats['success'] && $stats['data']['status'] === 'enabled' ? 'status-enabled' : 'status-disabled'; ?>">
                            <?php echo $stats['success'] ? ($stats['data']['status'] === 'enabled' ? 'Ativo' : 'Inativo') : 'Erro'; ?>
                        </span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Servidor:</span>
                        <span class="status-enabled"><?php echo PIHOLE_IP; ?></span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Versão:</span>
                        <span><?php echo $systemInfo['success'] ? $systemInfo['data']['pihole_version'] : 'N/A'; ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grid de Estatísticas -->
        <div class="stats-grid">
            <!-- Total de Queries -->
            <div class="stat-card fade-in-up">
                <div class="stat-header">
                    <span class="stat-title">Queries Hoje</span>
                    <span class="stat-icon">📊</span>
                </div>
                <div class="stat-value" id="total-queries">
                    <?php echo $stats['success'] ? number_format($stats['data']['dns_queries_today']) : '0'; ?>
                </div>
                <div class="stat-description">Total de consultas DNS processadas</div>
            </div>

            <!-- Queries Bloqueadas -->
            <div class="stat-card fade-in-up">
                <div class="stat-header">
                    <span class="stat-title">Bloqueadas Hoje</span>
                    <span class="stat-icon">🚫</span>
                </div>
                <div class="stat-value" id="blocked-queries" style="color: var(--danger-color);">
                    <?php echo $stats['success'] ? number_format($stats['data']['ads_blocked_today']) : '0'; ?>
                </div>
                <div class="stat-description">Consultas bloqueadas por filtros</div>
            </div>

            <!-- Percentual de Bloqueio -->
            <div class="stat-card fade-in-up">
                <div class="stat-header">
                    <span class="stat-title">% Bloqueadas</span>
                    <span class="stat-icon">📈</span>
                </div>
                <div class="stat-value" id="block-percentage" style="color: var(--warning-color);">
                    <?php echo $stats['success'] ? $stats['data']['ads_percentage_today'] . '%' : '0%'; ?>
                </div>
                <div class="stat-description">Percentual de bloqueio efetivo</div>
            </div>

            <!-- Domínios Únicos -->
            <div class="stat-card fade-in-up">
                <div class="stat-header">
                    <span class="stat-title">Domínios Únicos</span>
                    <span class="stat-icon">🌐</span>
                </div>
                <div class="stat-value" id="unique-domains" style="color: var(--accent-color);">
                    <?php echo $stats['success'] ? number_format($stats['data']['unique_domains']) : '0'; ?>
                </div>
                <div class="stat-description">Domínios consultados hoje</div>
            </div>

            <!-- Clientes Únicos -->
            <div class="stat-card fade-in-up">
                <div class="stat-header">
                    <span class="stat-title">Clientes Únicos</span>
                    <span class="stat-icon">👥</span>
                </div>
                <div class="stat-value" id="unique-clients" style="color: var(--success-color);">
                    <?php echo $stats['success'] ? number_format($stats['data']['unique_clients']) : '0'; ?>
                </div>
                <div class="stat-description">Dispositivos ativos na rede</div>
            </div>

            <!-- Queries Encaminhadas -->
            <div class="stat-card fade-in-up">
                <div class="stat-header">
                    <span class="stat-title">Encaminhadas</span>
                    <span class="stat-icon">↗️</span>
                </div>
                <div class="stat-value" id="queries-forwarded">
                    <?php echo $stats['success'] ? number_format($stats['data']['queries_forwarded']) : '0'; ?>
                </div>
                <div class="stat-description">Queries enviadas para upstream</div>
            </div>
        </div>

        <!-- Seção de Gráficos -->
        <div class="charts-section mt-3">
            <div class="chart-container">
                <div class="stat-card">
                    <div class="stat-header">
                        <h3>Visão Geral - Queries de Hoje</h3>
                        <div class="chart-controls">
                            <select id="chart-period">
                                <option value="today">Hoje</option>
                                <option value="week">Esta Semana</option>
                                <option value="month">Este Mês</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="overview-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Adicionais -->
        <div class="info-section mt-3">
            <div class="stat-card">
                <div class="stat-header">
                    <h3>Informações do Sistema</h3>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Última Atualização:</span>
                        <span id="last-update"><?php echo date('d/m/Y H:i:s'); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Atualização Automática:</span>
                        <label class="switch">
                            <input type="checkbox" id="auto-refresh" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Intervalo de Atualização:</span>
                        <span><?php echo REFRESH_INTERVAL; ?> segundos</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/dashboard.js"></script>
    
    <script>
        // Configurações específicas da página
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar gráfico de visão geral
            initOverviewChart();
        });

        async function initOverviewChart() {
            try {
                const response = await fetch('api/get_charts.php?type=overview');
                const data = await response.json();
                
                if (data.success) {
                    const ctx = document.getElementById('overview-chart').getContext('2d');
                    new Chart(ctx, {
                        type: data.data.type,
                        data: data.data,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Erro ao carregar gráfico:', error);
            }
        }
    </script>

    <style>
        .status-section .stat-card {
            margin-bottom: 0;
        }
        
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            background: var(--light-color);
            border-radius: 0.5rem;
        }
        
        .status-label {
            font-weight: 600;
            color: var(--text-secondary);
        }
        
        .controls {
            display: flex;
            gap: 0.5rem;
        }
        
        .chart-container {
            margin-top: 2rem;
        }
        
        .chart-wrapper {
            height: 300px;
            padding: 1rem;
        }
        
        .chart-controls select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.25rem;
            background: white;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: var(--light-color);
            border-radius: 0.5rem;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--text-secondary);
        }
        
        /* Switch toggle */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--success-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</body>
</html>
