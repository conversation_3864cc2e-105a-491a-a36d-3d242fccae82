<?php
/**
 * Classe para comunicação com a API do Zabbix
 * Desenvolvida para o Dashboard Megainffo Style
 */

class ZabbixAPI {
    private $apiUrl;
    private $apiToken;
    private $timeout;
    private $cache;
    private $cacheEnabled;
    private $cacheDuration;
    private $requestId;

    public function __construct($apiUrl = null, $apiToken = null) {
        $this->apiUrl = $apiUrl ?: ZABBIX_API_URL;
        $this->apiToken = $apiToken ?: ZABBIX_API_TOKEN;
        $this->timeout = 30;
        $this->cache = [];
        $this->cacheEnabled = ZABBIX_CACHE_ENABLED;
        $this->cacheDuration = ZABBIX_CACHE_DURATION;
        $this->requestId = 1;
    }

    /**
     * Fazer requisição para a API do Zabbix
     */
    private function makeRequest($method, $params = []) {
        $cacheKey = md5($method . serialize($params));
        
        // Verificar cache
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            $cached = $this->cache[$cacheKey];
            if (time() - $cached['timestamp'] < $this->cacheDuration) {
                return $cached['data'];
            }
        }

        // Preparar dados da requisição
        $requestData = [
            'jsonrpc' => '2.0',
            'method' => $method,
            'params' => $params,
            'auth' => $this->apiToken,
            'id' => $this->requestId++
        ];

        // Configurar contexto da requisição
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json-rpc',
                    'User-Agent: ZabbixDashboard/1.0'
                ],
                'content' => json_encode($requestData),
                'timeout' => $this->timeout
            ]
        ]);

        // Fazer requisição
        $response = @file_get_contents($this->apiUrl, false, $context);
        
        if ($response === false) {
            throw new Exception('Erro ao conectar com o Zabbix em ' . $this->apiUrl);
        }

        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Resposta inválida da API do Zabbix');
        }

        if (isset($data['error'])) {
            throw new Exception('Erro da API Zabbix: ' . $data['error']['message']);
        }

        $result = $data['result'] ?? null;

        // Salvar no cache
        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = [
                'data' => $result,
                'timestamp' => time()
            ];
        }

        return $result;
    }

    /**
     * Obter todos os hosts
     */
    public function getHosts($groupIds = null) {
        try {
            $params = [
                'output' => ['hostid', 'host', 'name', 'status', 'available'],
                'selectGroups' => ['groupid', 'name'],
                'selectInterfaces' => ['interfaceid', 'ip', 'dns', 'port'],
                'monitored' => true
            ];

            if ($groupIds) {
                $params['groupids'] = $groupIds;
            }

            $hosts = $this->makeRequest('host.get', $params);
            
            return [
                'success' => true,
                'data' => $hosts
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter grupos de hosts
     */
    public function getHostGroups() {
        try {
            $groups = $this->makeRequest('hostgroup.get', [
                'output' => ['groupid', 'name'],
                'real_hosts' => true
            ]);
            
            return [
                'success' => true,
                'data' => $groups
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter items de um host específico
     */
    public function getHostItems($hostId, $itemKeys = null) {
        try {
            $params = [
                'output' => ['itemid', 'name', 'key_', 'lastvalue', 'units', 'status'],
                'hostids' => $hostId,
                'monitored' => true,
                'webitems' => false
            ];

            if ($itemKeys) {
                $params['search'] = ['key_' => $itemKeys];
                $params['searchWildcardsEnabled'] => true;
            }

            $items = $this->makeRequest('item.get', $params);
            
            return [
                'success' => true,
                'data' => $items
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter dados históricos de um item
     */
    public function getHistory($itemId, $timeFrom = null, $timeTo = null, $limit = 100) {
        try {
            $params = [
                'output' => 'extend',
                'itemids' => $itemId,
                'sortfield' => 'clock',
                'sortorder' => 'DESC',
                'limit' => $limit
            ];

            if ($timeFrom) {
                $params['time_from'] = $timeFrom;
            }
            
            if ($timeTo) {
                $params['time_till'] = $timeTo;
            }

            $history = $this->makeRequest('history.get', $params);
            
            return [
                'success' => true,
                'data' => $history
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter problemas ativos
     */
    public function getProblems($hostIds = null, $severities = null) {
        try {
            $params = [
                'output' => ['eventid', 'objectid', 'name', 'severity', 'clock'],
                'selectHosts' => ['hostid', 'host', 'name'],
                'selectTriggers' => ['triggerid', 'description', 'priority'],
                'recent' => true,
                'sortfield' => ['severity', 'clock'],
                'sortorder' => ['DESC', 'DESC']
            ];

            if ($hostIds) {
                $params['hostids'] = $hostIds;
            }

            if ($severities) {
                $params['severities'] = $severities;
            }

            $problems = $this->makeRequest('problem.get', $params);
            
            return [
                'success' => true,
                'data' => $problems
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter métricas principais de um host
     */
    public function getHostMetrics($hostId) {
        try {
            $metrics = [
                'cpu' => [],
                'memory' => [],
                'disk' => [],
                'network' => []
            ];

            // Buscar items principais
            $items = $this->getHostItems($hostId);
            
            if (!$items['success']) {
                throw new Exception($items['message']);
            }

            foreach ($items['data'] as $item) {
                $key = $item['key_'];
                $value = $item['lastvalue'];
                $units = $item['units'];

                // Classificar por tipo de métrica
                if (strpos($key, 'cpu') !== false || strpos($key, 'proc') !== false) {
                    $metrics['cpu'][] = [
                        'name' => $item['name'],
                        'key' => $key,
                        'value' => $value,
                        'units' => $units
                    ];
                } elseif (strpos($key, 'memory') !== false || strpos($key, 'swap') !== false) {
                    $metrics['memory'][] = [
                        'name' => $item['name'],
                        'key' => $key,
                        'value' => $value,
                        'units' => $units
                    ];
                } elseif (strpos($key, 'disk') !== false || strpos($key, 'vfs.fs') !== false) {
                    $metrics['disk'][] = [
                        'name' => $item['name'],
                        'key' => $key,
                        'value' => $value,
                        'units' => $units
                    ];
                } elseif (strpos($key, 'net.if') !== false || strpos($key, 'icmp') !== false) {
                    $metrics['network'][] = [
                        'name' => $item['name'],
                        'key' => $key,
                        'value' => $value,
                        'units' => $units
                    ];
                }
            }

            return [
                'success' => true,
                'data' => $metrics
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter resumo geral do sistema
     */
    public function getSystemSummary() {
        try {
            $hosts = $this->getHosts();
            $problems = $this->getProblems();
            
            if (!$hosts['success']) {
                throw new Exception($hosts['message']);
            }

            $summary = [
                'total_hosts' => count($hosts['data']),
                'hosts_up' => 0,
                'hosts_down' => 0,
                'hosts_unknown' => 0,
                'total_problems' => 0,
                'critical_problems' => 0,
                'warning_problems' => 0
            ];

            // Contar status dos hosts
            foreach ($hosts['data'] as $host) {
                switch ($host['available']) {
                    case '1':
                        $summary['hosts_up']++;
                        break;
                    case '2':
                        $summary['hosts_down']++;
                        break;
                    default:
                        $summary['hosts_unknown']++;
                }
            }

            // Contar problemas por severidade
            if ($problems['success']) {
                $summary['total_problems'] = count($problems['data']);
                
                foreach ($problems['data'] as $problem) {
                    $severity = $problem['severity'];
                    if ($severity >= 4) { // High/Disaster
                        $summary['critical_problems']++;
                    } elseif ($severity >= 2) { // Warning/Average
                        $summary['warning_problems']++;
                    }
                }
            }

            return [
                'success' => true,
                'data' => $summary
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Testar conectividade com o Zabbix
     */
    public function testConnection() {
        try {
            $result = $this->makeRequest('apiinfo.version');
            
            return [
                'success' => true,
                'data' => [
                    'version' => $result,
                    'connection' => 'OK'
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Limpar cache
     */
    public function clearCache() {
        $this->cache = [];
        return true;
    }

    /**
     * Formatar bytes para unidade legível
     */
    public static function formatBytes($bytes) {
        if ($bytes == 0) return '0 B';
        
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes, 1024));
        
        return sprintf('%.2f %s', $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * Calcular percentual de uso
     */
    public static function calculatePercentage($used, $total) {
        if ($total == 0) return 0;
        return round(($used / $total) * 100, 2);
    }
}
?>
