<?php
/**
 * Demonstração das Funcionalidades do Dashboard Zabbix
 * Este arquivo mostra como usar todas as funcionalidades da API
 */

require_once 'config/config.php';
require_once 'classes/ZabbixAPI.php';

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstração - Dashboard Zabbix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            border-radius: 1rem;
        }
        .demo-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .success { color: #10b981; background: #ecfdf5; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #10b981; }
        .error { color: #ef4444; background: #fef2f2; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #ef4444; }
        .info { color: #3b82f6; background: #eff6ff; padding: 0.75rem; border-radius: 0.5rem; border-left: 4px solid #3b82f6; }
        .demo-title { font-size: 1.2rem; font-weight: bold; margin-bottom: 1rem; color: #1e3a8a; }
        pre { background: #f3f4f6; padding: 1rem; border-radius: 0.5rem; overflow-x: auto; font-size: 0.9rem; }
        .btn { background: #3b82f6; color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 0.5rem; cursor: pointer; text-decoration: none; display: inline-block; margin: 0.5rem; }
        .btn:hover { background: #2563eb; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; }
        .metric-card { background: #f8fafc; padding: 1rem; border-radius: 0.5rem; border: 1px solid #e5e7eb; }
        .metric-value { font-size: 1.5rem; font-weight: bold; color: #1e3a8a; }
        .metric-label { font-size: 0.9rem; color: #6b7280; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Demonstração Dashboard Zabbix</h1>
        <p>Testando todas as funcionalidades da API</p>
    </div>

    <?php
    try {
        $zabbix = new ZabbixAPI();
        
        // Demo 1: Teste de Conectividade
        echo '<div class="demo-section">';
        echo '<div class="demo-title">🔗 Demo 1: Teste de Conectividade</div>';
        
        $connection = $zabbix->testConnection();
        if ($connection['success']) {
            echo '<div class="success">✅ Conectado com sucesso ao Zabbix!</div>';
            echo '<div class="info">Versão da API: ' . $connection['data']['version'] . '</div>';
        } else {
            echo '<div class="error">❌ Erro de conexão: ' . $connection['message'] . '</div>';
        }
        echo '</div>';

        // Demo 2: Resumo do Sistema
        echo '<div class="demo-section">';
        echo '<div class="demo-title">📊 Demo 2: Resumo do Sistema</div>';
        
        $summary = $zabbix->getSystemSummary();
        if ($summary['success']) {
            echo '<div class="success">✅ Resumo obtido com sucesso!</div>';
            echo '<div class="grid">';
            echo '<div class="metric-card">';
            echo '<div class="metric-value">' . $summary['data']['total_hosts'] . '</div>';
            echo '<div class="metric-label">Total de Hosts</div>';
            echo '</div>';
            echo '<div class="metric-card">';
            echo '<div class="metric-value">' . $summary['data']['hosts_up'] . '</div>';
            echo '<div class="metric-label">Hosts Online</div>';
            echo '</div>';
            echo '<div class="metric-card">';
            echo '<div class="metric-value">' . $summary['data']['critical_problems'] . '</div>';
            echo '<div class="metric-label">Problemas Críticos</div>';
            echo '</div>';
            echo '<div class="metric-card">';
            echo '<div class="metric-value">' . $summary['data']['warning_problems'] . '</div>';
            echo '<div class="metric-label">Avisos</div>';
            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="error">❌ Erro: ' . $summary['message'] . '</div>';
        }
        echo '</div>';

        // Demo 3: Lista de Hosts
        echo '<div class="demo-section">';
        echo '<div class="demo-title">🖥️ Demo 3: Lista de Hosts</div>';
        
        $hosts = $zabbix->getHosts();
        if ($hosts['success']) {
            echo '<div class="success">✅ ' . count($hosts['data']) . ' hosts encontrados!</div>';
            
            if (!empty($hosts['data'])) {
                echo '<div class="info">Primeiros 5 hosts:</div>';
                echo '<pre>';
                foreach (array_slice($hosts['data'], 0, 5) as $host) {
                    $status = $host['available'] == '1' ? 'Online' : ($host['available'] == '2' ? 'Offline' : 'Desconhecido');
                    echo "• {$host['name']} ({$host['host']}) - {$status}\n";
                    if (!empty($host['groups'])) {
                        echo "  Grupos: " . implode(', ', array_column($host['groups'], 'name')) . "\n";
                    }
                    if (!empty($host['interfaces'])) {
                        echo "  IP: " . $host['interfaces'][0]['ip'] . "\n";
                    }
                    echo "\n";
                }
                echo '</pre>';
            }
        } else {
            echo '<div class="error">❌ Erro: ' . $hosts['message'] . '</div>';
        }
        echo '</div>';

        // Demo 4: Problemas Ativos
        echo '<div class="demo-section">';
        echo '<div class="demo-title">🚨 Demo 4: Problemas Ativos</div>';
        
        $problems = $zabbix->getProblems();
        if ($problems['success']) {
            if (count($problems['data']) > 0) {
                echo '<div class="error">⚠️ ' . count($problems['data']) . ' problemas encontrados!</div>';
                echo '<div class="info">Primeiros 3 problemas:</div>';
                echo '<pre>';
                foreach (array_slice($problems['data'], 0, 3) as $problem) {
                    $severityMap = ['Não classificado', 'Informação', 'Atenção', 'Média', 'Alta', 'Desastre'];
                    $severity = $severityMap[$problem['severity']] ?? 'Desconhecido';
                    echo "• {$problem['name']}\n";
                    echo "  Severidade: {$severity}\n";
                    echo "  Tempo: " . date('Y-m-d H:i:s', $problem['clock']) . "\n";
                    if (!empty($problem['hosts'])) {
                        echo "  Hosts: " . implode(', ', array_column($problem['hosts'], 'name')) . "\n";
                    }
                    echo "\n";
                }
                echo '</pre>';
            } else {
                echo '<div class="success">🎉 Nenhum problema ativo - Sistema funcionando normalmente!</div>';
            }
        } else {
            echo '<div class="error">❌ Erro: ' . $problems['message'] . '</div>';
        }
        echo '</div>';

        // Demo 5: Métricas de um Host
        if ($hosts['success'] && !empty($hosts['data'])) {
            $firstHost = $hosts['data'][0];
            
            echo '<div class="demo-section">';
            echo '<div class="demo-title">📈 Demo 5: Métricas do Host "' . $firstHost['name'] . '"</div>';
            
            $metrics = $zabbix->getHostMetrics($firstHost['hostid']);
            if ($metrics['success']) {
                echo '<div class="success">✅ Métricas obtidas com sucesso!</div>';
                
                foreach (['cpu', 'memory', 'disk', 'network'] as $type) {
                    if (!empty($metrics['data'][$type])) {
                        echo '<div class="info">📊 ' . strtoupper($type) . ' (' . count($metrics['data'][$type]) . ' itens):</div>';
                        echo '<pre>';
                        foreach (array_slice($metrics['data'][$type], 0, 3) as $metric) {
                            echo "• {$metric['name']}: {$metric['value']} {$metric['units']}\n";
                        }
                        echo '</pre>';
                    }
                }
            } else {
                echo '<div class="error">❌ Erro: ' . $metrics['message'] . '</div>';
            }
            echo '</div>';
        }

        // Demo 6: Grupos de Hosts
        echo '<div class="demo-section">';
        echo '<div class="demo-title">👥 Demo 6: Grupos de Hosts</div>';
        
        $groups = $zabbix->getHostGroups();
        if ($groups['success']) {
            echo '<div class="success">✅ ' . count($groups['data']) . ' grupos encontrados!</div>';
            
            if (!empty($groups['data'])) {
                echo '<div class="info">Grupos disponíveis:</div>';
                echo '<pre>';
                foreach (array_slice($groups['data'], 0, 10) as $group) {
                    echo "• {$group['name']} (ID: {$group['groupid']})\n";
                }
                echo '</pre>';
            }
        } else {
            echo '<div class="error">❌ Erro: ' . $groups['message'] . '</div>';
        }
        echo '</div>';

    } catch (Exception $e) {
        echo '<div class="demo-section">';
        echo '<div class="error">❌ Erro geral: ' . $e->getMessage() . '</div>';
        echo '</div>';
    }
    ?>

    <div class="demo-section">
        <div class="demo-title">🚀 Próximos Passos</div>
        <div class="info">
            <p>Se todas as demonstrações funcionaram, você pode:</p>
            <ul>
                <li>Acessar o dashboard principal</li>
                <li>Explorar as páginas de hosts e problemas</li>
                <li>Configurar alertas personalizados</li>
                <li>Personalizar as métricas monitoradas</li>
            </ul>
            <br>
            <a href="index.php" class="btn">🏠 Dashboard Principal</a>
            <a href="pages/hosts.php" class="btn">🖥️ Hosts</a>
            <a href="pages/problems.php" class="btn">🚨 Problemas</a>
            <a href="test_connection.php" class="btn">🔍 Teste de Conectividade</a>
        </div>
    </div>

    <div class="demo-section">
        <div class="demo-title">⚙️ Configurações Atuais</div>
        <div class="grid">
            <div>
                <strong>URL do Zabbix:</strong><br>
                <?php echo ZABBIX_URL; ?>
            </div>
            <div>
                <strong>Token da API:</strong><br>
                <?php echo substr(ZABBIX_API_TOKEN, 0, 10) . '...'; ?>
            </div>
            <div>
                <strong>Cache:</strong><br>
                <?php echo ZABBIX_CACHE_ENABLED ? 'Habilitado' : 'Desabilitado'; ?>
            </div>
            <div>
                <strong>Atualização:</strong><br>
                <?php echo ZABBIX_REFRESH_INTERVAL; ?> segundos
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh da página a cada 60 segundos
        setTimeout(() => location.reload(), 60000);
        
        // Adicionar timestamp na página
        document.addEventListener('DOMContentLoaded', function() {
            const timestamp = document.createElement('div');
            timestamp.style.cssText = 'text-align: center; margin-top: 2rem; color: #6b7280; font-size: 0.9rem;';
            timestamp.textContent = 'Última atualização: ' + new Date().toLocaleString('pt-BR');
            document.body.appendChild(timestamp);
        });
    </script>
</body>
</html>
