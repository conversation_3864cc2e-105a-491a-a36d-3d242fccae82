# Configuração Nginx para Dashboard Pi-hole
# Copie este arquivo para /etc/nginx/sites-available/pihole-dashboard
# e crie um link simbólico em /etc/nginx/sites-enabled/

server {
    listen 80;
    listen [::]:80;
    
    # Substitua pelo seu domínio ou IP
    server_name pihole-dashboard.local *************;
    
    # Caminho para os arquivos do dashboard
    root /var/www/html/DashboardMegainffo;
    index index.php index.html index.htm;
    
    # Configurações de segurança
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Logs
    access_log /var/log/nginx/pihole-dashboard.access.log;
    error_log /var/log/nginx/pihole-dashboard.error.log;
    
    # Configuração principal
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Processar arquivos PHP
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock; # Ajuste a versão do PHP
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # Configurações de timeout
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }
    
    # Cache para arquivos estáticos
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Bloquear acesso a arquivos sensíveis
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(config|classes)/.*\.php$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # API endpoints com CORS
    location /api/ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type, Authorization";
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain charset=UTF-8';
            add_header Content-Length 0;
            return 204;
        }
        
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    # Compressão GZIP
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}

# Configuração HTTPS (opcional)
# Descomente e configure se você tiver certificado SSL
#
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     
#     server_name pihole-dashboard.local;
#     
#     # Certificados SSL
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # Configurações SSL modernas
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # Resto da configuração igual ao bloco HTTP acima
#     root /var/www/html/DashboardMegainffo;
#     index index.php index.html index.htm;
#     
#     # ... (copie o resto da configuração do bloco HTTP)
# }
#
# # Redirecionar HTTP para HTTPS
# server {
#     listen 80;
#     listen [::]:80;
#     server_name pihole-dashboard.local;
#     return 301 https://$server_name$request_uri;
# }
