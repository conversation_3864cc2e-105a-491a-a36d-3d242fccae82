# Dashboard Zabbix - Estilo Megainffo

Um dashboard completo e personalizado para Zabbix com design inspirado no site da Megainffo, desenvolvido em PHP, HTML e CSS para monitoramento de servidores.

## 🚀 Características

- **Design Moderno**: Interface inspirada no site da Megainffo com cores e tipografia profissionais
- **Monitoramento Completo**: CPU, RAM, disco e rede de todos os servidores
- **Tempo Real**: Atualização automática das métricas a cada 30 segundos
- **Gráficos Interativos**: Visualizações com Chart.js para análise histórica
- **API Completa**: Comunicação eficiente com a API JSON-RPC do Zabbix
- **Cache Inteligente**: Sistema de cache para melhor performance
- **Responsivo**: Funciona perfeitamente em desktop, tablet e mobile

## 📊 Funcionalidades

### Dashboard Principal
- Resumo geral do sistema (servidores online/offline, problemas)
- Cards de servidores com métricas em tempo real
- Gráficos de status e uso de recursos
- Lista de problemas recentes

### Páginas Especializadas
- **Servidores**: Lista completa com filtros e visualizações
- **Problemas**: Alertas e triggers ativos do Zabbix
- **Gráficos**: Visualizações históricas e comparativas
- **Configurações**: Ajustes do dashboard

### Métricas Monitoradas
- **CPU**: Utilização, load average
- **Memória**: RAM utilizada, disponível, swap
- **Disco**: Espaço usado, disponível por filesystem
- **Rede**: Tráfego de entrada/saída, conectividade

## 🛠️ Instalação

### Pré-requisitos
- Servidor web com PHP 7.4+ (Apache/Nginx)
- Zabbix Server funcionando (versão 5.0+)
- Token de API do Zabbix configurado
- Extensões PHP: `json`, `curl`

### Passos de Instalação

1. **Clone ou baixe o projeto**
   ```bash
   git clone [url-do-repositorio]
   cd DashboardMegainffo/zabbix
   ```

2. **Configure o servidor web**
   - Aponte o DocumentRoot para a pasta `zabbix`
   - Certifique-se que o PHP está habilitado

3. **Configure o Zabbix**
   - Edite o arquivo `config/config.php`
   - Configure a URL e token da API:
   ```php
   define('ZABBIX_URL', 'http://*************/zabbix/');
   define('ZABBIX_API_TOKEN', 'seu_token_aqui');
   ```

4. **Obter Token da API do Zabbix**
   - Acesse a interface web do Zabbix
   - Vá em User Settings > API tokens
   - Crie um novo token ou use um existente
   - Token configurado: `9fd2877a0701b9ba124035b9569664a70a88319df28c6b987e1611894493fd39`

5. **Teste a instalação**
   - Acesse `http://seu-servidor/test_connection.php`
   - Verifique se todos os testes passam
   - Acesse `http://seu-servidor/` para o dashboard

## 🔧 Configuração

### Estrutura de Arquivos
```
zabbix/
├── config/
│   └── config.php          # Configurações principais
├── classes/
│   └── ZabbixAPI.php       # Classe para comunicação com Zabbix
├── api/
│   ├── get_hosts.php       # Endpoint para hosts
│   ├── get_summary.php     # Endpoint para resumo
│   ├── get_problems.php    # Endpoint para problemas
│   └── get_charts.php      # Endpoint para gráficos
├── assets/
│   ├── css/
│   │   └── zabbix-style.css # Estilos principais
│   └── js/
│       └── zabbix-dashboard.js # JavaScript do dashboard
├── pages/
│   ├── hosts.php           # Página de servidores
│   ├── problems.php        # Página de problemas
│   └── charts.php          # Página de gráficos
├── index.php               # Página principal
├── test_connection.php     # Script de teste
└── README.md               # Este arquivo
```

### Configurações Avançadas

#### Cache
O sistema de cache pode ser configurado no arquivo `config/config.php`:
```php
define('ZABBIX_CACHE_ENABLED', true);
define('ZABBIX_CACHE_DURATION', 60); // segundos
```

#### Itens Monitorados
Configure quais itens do Zabbix devem ser monitorados:
```php
define('ZABBIX_MAIN_ITEMS', [
    'cpu' => ['system.cpu.util', 'system.cpu.load[percpu,avg1]'],
    'memory' => ['vm.memory.util', 'vm.memory.size[available]'],
    'disk' => ['vfs.fs.size[/,pused]', 'vfs.fs.size[/,used]'],
    'network' => ['net.if.in[eth0]', 'net.if.out[eth0]']
]);
```

#### Limites de Alerta
Defina limites para alertas visuais:
```php
define('ZABBIX_THRESHOLDS', [
    'cpu' => ['warning' => 70, 'critical' => 90],
    'memory' => ['warning' => 80, 'critical' => 95],
    'disk' => ['warning' => 80, 'critical' => 95]
]);
```

## 🎨 Design

O dashboard foi inspirado no design da Megainffo, incorporando:
- **Paleta de cores**: Azuis profissionais com acentos em ciano
- **Tipografia**: Segoe UI para legibilidade moderna
- **Layout**: Grid responsivo com cards e gradientes
- **Animações**: Transições suaves e efeitos hover
- **Iconografia**: Emojis e símbolos para melhor UX

## 📱 Responsividade

O dashboard é totalmente responsivo e se adapta a:
- **Desktop**: Layout completo com múltiplas colunas
- **Tablet**: Layout adaptado com navegação otimizada
- **Mobile**: Interface compacta com navegação em menu

## 🔌 API

### Endpoints Disponíveis

#### GET `/api/get_hosts.php`
Retorna lista de hosts com métricas
```json
{
    "success": true,
    "data": [
        {
            "hostid": "10001",
            "hostname": "server01",
            "name": "Servidor Web",
            "status": "1",
            "metrics": {
                "cpu": {"usage": 45.2, "status": "ok"},
                "memory": {"usage": 67.8, "status": "warning"}
            }
        }
    ]
}
```

#### GET `/api/get_summary.php`
Retorna resumo geral do sistema
```json
{
    "success": true,
    "data": {
        "total_hosts": 15,
        "hosts_up": 14,
        "hosts_down": 1,
        "critical_problems": 2,
        "warning_problems": 5
    }
}
```

#### GET `/api/get_problems.php`
Retorna problemas ativos
```json
{
    "success": true,
    "data": [
        {
            "eventid": "12345",
            "name": "High CPU usage",
            "severity": 4,
            "severity_text": "Alta",
            "hosts": [{"name": "Servidor Web"}]
        }
    ]
}
```

#### GET `/api/get_charts.php?type=overview`
Retorna dados para gráficos
- `type=overview`: Status geral dos hosts
- `type=cpu_usage`: Uso de CPU por host
- `type=memory_usage`: Uso de memória por host
- `type=problems_severity`: Problemas por severidade

## 🚨 Solução de Problemas

### Erro de Conexão com Zabbix
1. Verifique se a URL está correta em `config/config.php`
2. Teste a conectividade: `curl http://*************/zabbix/`
3. Verifique se a API está habilitada no Zabbix

### Token de API Inválido
1. Acesse User Settings > API tokens no Zabbix
2. Verifique se o token não expirou
3. Crie um novo token se necessário

### Dados não Carregam
1. Execute `test_connection.php` para diagnóstico
2. Verifique os logs do servidor web
3. Confirme se os hosts têm os itens configurados

### Performance Lenta
1. Habilite o cache em `config/config.php`
2. Ajuste o `CACHE_DURATION`
3. Reduza o número de hosts monitorados

## 🔒 Segurança

- Use HTTPS em produção
- Mantenha o token da API seguro
- Configure autenticação se necessário
- Restrinja acesso por IP se possível

## 📈 Próximas Funcionalidades

- [ ] Página de detalhes por host
- [ ] Gráficos históricos avançados
- [ ] Notificações push
- [ ] Exportação de relatórios
- [ ] Modo escuro
- [ ] Múltiplos servidores Zabbix

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## 🙏 Agradecimentos

- **Zabbix Team**: Pela excelente ferramenta de monitoramento
- **Megainffo**: Pela inspiração do design
- **Chart.js**: Pelos gráficos interativos
- **Comunidade PHP**: Pelas melhores práticas

---

**Desenvolvido com ❤️ para a comunidade Zabbix**
