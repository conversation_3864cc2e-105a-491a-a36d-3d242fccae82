<?php
/**
 * API Endpoint - Obter hosts e suas métricas
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/ZabbixAPI.php';

try {
    // Criar instância da API
    $zabbix = new ZabbixAPI();
    
    // Obter parâmetros
    $groupId = $_GET['group_id'] ?? null;
    $includeMetrics = $_GET['include_metrics'] ?? true;
    
    // Obter hosts
    $hosts = $zabbix->getHosts($groupId ? [$groupId] : null);
    
    if (!$hosts['success']) {
        throw new Exception($hosts['message']);
    }
    
    $hostsData = [];
    
    foreach ($hosts['data'] as $host) {
        $hostData = [
            'hostid' => $host['hostid'],
            'hostname' => $host['host'],
            'name' => $host['name'],
            'status' => $host['status'],
            'available' => $host['available'],
            'groups' => $host['groups'],
            'interfaces' => $host['interfaces']
        ];
        
        // Determinar status geral
        switch ($host['available']) {
            case '1':
                $hostData['status_text'] = 'Online';
                $hostData['status_class'] = 'ok';
                break;
            case '2':
                $hostData['status_text'] = 'Offline';
                $hostData['status_class'] = 'critical';
                break;
            default:
                $hostData['status_text'] = 'Desconhecido';
                $hostData['status_class'] = 'unknown';
        }
        
        // Obter métricas se solicitado
        if ($includeMetrics) {
            $metrics = $zabbix->getHostMetrics($host['hostid']);
            
            if ($metrics['success']) {
                $hostData['metrics'] = [
                    'cpu' => $this->processMetrics($metrics['data']['cpu']),
                    'memory' => $this->processMetrics($metrics['data']['memory']),
                    'disk' => $this->processMetrics($metrics['data']['disk']),
                    'network' => $this->processMetrics($metrics['data']['network'])
                ];
            } else {
                $hostData['metrics'] = [
                    'cpu' => ['usage' => 0, 'status' => 'unknown'],
                    'memory' => ['usage' => 0, 'status' => 'unknown'],
                    'disk' => ['usage' => 0, 'status' => 'unknown'],
                    'network' => ['status' => 'unknown']
                ];
            }
        }
        
        $hostsData[] = $hostData;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $hostsData,
        'total' => count($hostsData),
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'API_ERROR'
    ]);
}

/**
 * Processar métricas para formato padronizado
 */
function processMetrics($metrics) {
    if (empty($metrics)) {
        return ['usage' => 0, 'status' => 'unknown'];
    }
    
    $result = ['usage' => 0, 'status' => 'ok', 'details' => []];
    
    foreach ($metrics as $metric) {
        $value = floatval($metric['value']);
        
        // Processar diferentes tipos de métricas
        if (strpos($metric['key'], 'util') !== false || 
            strpos($metric['key'], 'pused') !== false) {
            // Percentual de uso
            $result['usage'] = max($result['usage'], $value);
        }
        
        $result['details'][] = [
            'name' => $metric['name'],
            'value' => $value,
            'units' => $metric['units'],
            'formatted' => formatMetricValue($value, $metric['units'])
        ];
    }
    
    // Determinar status baseado no uso
    if ($result['usage'] >= 90) {
        $result['status'] = 'critical';
    } elseif ($result['usage'] >= 70) {
        $result['status'] = 'warning';
    }
    
    return $result;
}

/**
 * Formatar valor da métrica
 */
function formatMetricValue($value, $units) {
    switch ($units) {
        case 'B':
        case 'bytes':
            return ZabbixAPI::formatBytes($value);
        case '%':
            return number_format($value, 1) . '%';
        case 's':
            return number_format($value, 2) . 's';
        default:
            return number_format($value, 2) . ($units ? ' ' . $units : '');
    }
}
?>
