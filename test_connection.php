<?php
/**
 * Script de Teste de Conectividade com Pi-hole
 * Execute este arquivo para verificar se a conexão está funcionando
 */

require_once 'config/config.php';
require_once 'classes/PiholeAPI.php';

// Configurar cabeçalho para exibição no navegador
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Conectividade - Dashboard Pi-hole</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            border-radius: 1rem;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #1e3a8a;
        }
        .success {
            color: #10b981;
            background: #ecfdf5;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 4px solid #10b981;
        }
        .error {
            color: #ef4444;
            background: #fef2f2;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 4px solid #ef4444;
        }
        .info {
            color: #3b82f6;
            background: #eff6ff;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 4px solid #3b82f6;
        }
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .config-table th,
        .config-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .config-table th {
            background: #f8fafc;
            font-weight: 600;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #1e3a8a;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        pre {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Teste de Conectividade</h1>
        <p>Dashboard Pi-hole - Megainffo Style</p>
    </div>

    <?php
    echo '<div class="test-section">';
    echo '<div class="test-title">📋 Configurações Atuais</div>';
    echo '<table class="config-table">';
    echo '<tr><th>Configuração</th><th>Valor</th></tr>';
    echo '<tr><td>IP do Pi-hole</td><td>' . PIHOLE_IP . '</td></tr>';
    echo '<tr><td>Porta</td><td>' . PIHOLE_PORT . '</td></tr>';
    echo '<tr><td>URL da API</td><td>' . PIHOLE_API_URL . '</td></tr>';
    echo '<tr><td>API Token</td><td>' . (defined('PIHOLE_API_TOKEN') && PIHOLE_API_TOKEN ? 'Configurado (' . substr(PIHOLE_API_TOKEN, 0, 10) . '...)' : 'Não configurado') . '</td></tr>';
    echo '<tr><td>Cache Habilitado</td><td>' . (CACHE_ENABLED ? 'Sim' : 'Não') . '</td></tr>';
    echo '<tr><td>Duração do Cache</td><td>' . CACHE_DURATION . ' segundos</td></tr>';
    echo '<tr><td>Intervalo de Atualização</td><td>' . REFRESH_INTERVAL . ' segundos</td></tr>';
    echo '<tr><td>Autenticação</td><td>' . (ENABLE_AUTH ? 'Habilitada' : 'Desabilitada') . '</td></tr>';
    echo '</table>';
    echo '</div>';

    // Teste 1: Verificar se o Pi-hole está acessível
    echo '<div class="test-section">';
    echo '<div class="test-title">🌐 Teste 1: Conectividade de Rede</div>';
    
    $ping_result = @fsockopen(PIHOLE_IP, PIHOLE_PORT, $errno, $errstr, 5);
    if ($ping_result) {
        echo '<div class="success">✅ Sucesso: Pi-hole está acessível em ' . PIHOLE_IP . ':' . PIHOLE_PORT . '</div>';
        fclose($ping_result);
    } else {
        echo '<div class="error">❌ Erro: Não foi possível conectar ao Pi-hole<br>';
        echo 'Erro: ' . $errstr . ' (Código: ' . $errno . ')</div>';
        echo '<div class="info">💡 Verifique se o IP e porta estão corretos e se o Pi-hole está funcionando.</div>';
    }
    echo '</div>';

    // Teste 2: Testar API do Pi-hole
    echo '<div class="test-section">';
    echo '<div class="test-title">🔌 Teste 2: API do Pi-hole</div>';
    
    try {
        $pihole = new PiholeAPI();
        $connection_test = $pihole->testConnection();
        
        if ($connection_test['success']) {
            echo '<div class="success">✅ Sucesso: API do Pi-hole está respondendo</div>';
            echo '<div class="info">Versão: ' . $connection_test['data']['version'] . '</div>';
        } else {
            echo '<div class="error">❌ Erro na API: ' . $connection_test['message'] . '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Teste 3: Obter estatísticas
    echo '<div class="test-section">';
    echo '<div class="test-title">📊 Teste 3: Estatísticas do Pi-hole</div>';
    
    try {
        $pihole = new PiholeAPI();
        $stats = $pihole->getStats();
        
        if ($stats['success']) {
            echo '<div class="success">✅ Sucesso: Estatísticas obtidas com sucesso</div>';
            echo '<pre>' . json_encode($stats['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        } else {
            echo '<div class="error">❌ Erro ao obter estatísticas: ' . $stats['message'] . '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Teste 4: Verificar funcionalidades administrativas
    echo '<div class="test-section">';
    echo '<div class="test-title">🔐 Teste 4: Funcionalidades Administrativas</div>';

    try {
        $pihole = new PiholeAPI();

        if (defined('PIHOLE_API_TOKEN') && PIHOLE_API_TOKEN) {
            echo '<div class="success">✅ API Token configurado</div>';

            // Teste apenas verificar se o token é aceito (sem alterar o estado)
            $stats = $pihole->getStats();
            if ($stats['success']) {
                echo '<div class="info">💡 Token válido - Funcionalidades administrativas disponíveis</div>';
                echo '<div class="info">Status atual do Pi-hole: <strong>' . $stats['data']['status'] . '</strong></div>';
            } else {
                echo '<div class="error">❌ Erro ao verificar status com token</div>';
            }
        } else {
            echo '<div class="error">❌ API Token não configurado</div>';
            echo '<div class="info">💡 Funcionalidades administrativas não estarão disponíveis</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Teste 5: Verificar extensões PHP
    echo '<div class="test-section">';
    echo '<div class="test-title">🐘 Teste 5: Extensões PHP</div>';
    
    $required_extensions = ['json', 'curl'];
    $all_ok = true;
    
    foreach ($required_extensions as $ext) {
        if (extension_loaded($ext)) {
            echo '<div class="success">✅ Extensão ' . $ext . ' está carregada</div>';
        } else {
            echo '<div class="error">❌ Extensão ' . $ext . ' não está carregada</div>';
            $all_ok = false;
        }
    }
    
    if ($all_ok) {
        echo '<div class="info">💡 Todas as extensões necessárias estão disponíveis.</div>';
    } else {
        echo '<div class="error">⚠️ Algumas extensões estão faltando. Instale-as para o funcionamento completo.</div>';
    }
    echo '</div>';

    // Teste 6: Verificar permissões de arquivo
    echo '<div class="test-section">';
    echo '<div class="test-title">📁 Teste 6: Permissões de Arquivo</div>';
    
    $files_to_check = [
        'config/config.php',
        'classes/PiholeAPI.php',
        'api/get_stats.php',
        'assets/css/style.css',
        'assets/js/dashboard.js'
    ];
    
    $all_readable = true;
    foreach ($files_to_check as $file) {
        if (file_exists($file) && is_readable($file)) {
            echo '<div class="success">✅ ' . $file . ' está acessível</div>';
        } else {
            echo '<div class="error">❌ ' . $file . ' não está acessível</div>';
            $all_readable = false;
        }
    }
    
    if ($all_readable) {
        echo '<div class="info">💡 Todos os arquivos necessários estão acessíveis.</div>';
    }
    echo '</div>';

    // Informações do sistema
    echo '<div class="test-section">';
    echo '<div class="test-title">ℹ️ Informações do Sistema</div>';
    echo '<table class="config-table">';
    echo '<tr><th>Item</th><th>Valor</th></tr>';
    echo '<tr><td>Versão do PHP</td><td>' . phpversion() . '</td></tr>';
    echo '<tr><td>Servidor Web</td><td>' . $_SERVER['SERVER_SOFTWARE'] . '</td></tr>';
    echo '<tr><td>Sistema Operacional</td><td>' . php_uname() . '</td></tr>';
    echo '<tr><td>Data/Hora</td><td>' . date('Y-m-d H:i:s') . '</td></tr>';
    echo '<tr><td>Timezone</td><td>' . date_default_timezone_get() . '</td></tr>';
    echo '</table>';
    echo '</div>';
    ?>

    <div class="test-section">
        <div class="test-title">🚀 Próximos Passos</div>
        <div class="info">
            Se todos os testes passaram, você pode acessar o dashboard principal:
            <br><br>
            <a href="index.php" class="btn">🏠 Ir para o Dashboard</a>
        </div>
        
        <?php if (!$ping_result): ?>
        <div class="error">
            <strong>Problemas encontrados:</strong><br>
            1. Verifique se o Pi-hole está funcionando<br>
            2. Confirme o IP em config/config.php<br>
            3. Teste a conectividade de rede<br>
            4. Verifique firewall e portas
        </div>
        <?php endif; ?>
    </div>

    <div class="test-section">
        <div class="test-title">📚 Documentação</div>
        <div class="info">
            Para mais informações, consulte o arquivo README.md ou a documentação do Pi-hole.
        </div>
    </div>
</body>
</html>
