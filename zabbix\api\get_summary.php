<?php
/**
 * API Endpoint - Obter resumo geral do sistema
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/ZabbixAPI.php';

try {
    // Criar instância da API
    $zabbix = new ZabbixAPI();
    
    // Obter resumo do sistema
    $summary = $zabbix->getSystemSummary();
    
    if (!$summary['success']) {
        throw new Exception($summary['message']);
    }
    
    $data = $summary['data'];
    
    // Calcular percentuais
    $data['hosts_up_percent'] = $data['total_hosts'] > 0 ? 
        round(($data['hosts_up'] / $data['total_hosts']) * 100, 1) : 0;
    
    $data['hosts_down_percent'] = $data['total_hosts'] > 0 ? 
        round(($data['hosts_down'] / $data['total_hosts']) * 100, 1) : 0;
    
    // Determinar status geral do sistema
    if ($data['hosts_down'] > 0 || $data['critical_problems'] > 0) {
        $data['system_status'] = 'critical';
        $data['system_status_text'] = 'Crítico';
    } elseif ($data['warning_problems'] > 0) {
        $data['system_status'] = 'warning';
        $data['system_status_text'] = 'Atenção';
    } else {
        $data['system_status'] = 'ok';
        $data['system_status_text'] = 'Normal';
    }
    
    // Adicionar timestamp
    $data['last_update'] = date('Y-m-d H:i:s');
    $data['timestamp'] = time();
    
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'API_ERROR'
    ]);
}
?>
