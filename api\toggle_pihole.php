<?php
/**
 * API Endpoint - Habilitar/Desabilitar Pi-hole
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Verificar método
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido'
    ]);
    exit;
}

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/PiholeAPI.php';

try {
    // Obter dados do POST
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['action'])) {
        throw new Exception('Ação não especificada');
    }
    
    $action = $input['action'];
    $duration = $input['duration'] ?? 0;
    
    // Validar ação
    if (!in_array($action, ['enable', 'disable'])) {
        throw new Exception('Ação inválida');
    }
    
    // Criar instância da API
    $pihole = new PiholeAPI();
    
    // Verificar se autenticação está habilitada
    if (ENABLE_AUTH) {
        $password = $input['password'] ?? '';
        if ($password !== ADMIN_PASSWORD) {
            throw new Exception('Senha incorreta');
        }
    }
    
    // Executar ação
    if ($action === 'enable') {
        $result = $pihole->enable();
    } else {
        $result = $pihole->disable($duration);
    }
    
    // Limpar cache após mudança de estado
    $pihole->clearCache();
    
    echo json_encode($result);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
