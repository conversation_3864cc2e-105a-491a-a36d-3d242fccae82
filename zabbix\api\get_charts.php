<?php
/**
 * API Endpoint - Obter dados para gráficos
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/ZabbixAPI.php';

try {
    // Criar instância da API
    $zabbix = new ZabbixAPI();
    
    // Obter parâmetros
    $type = $_GET['type'] ?? 'overview';
    $hostId = $_GET['host_id'] ?? null;
    $period = $_GET['period'] ?? '1h';
    
    // Calcular período de tempo
    $timeFrom = time() - getPeriodSeconds($period);
    $timeTo = time();
    
    switch ($type) {
        case 'overview':
            // Gráfico de visão geral - status dos hosts
            $hosts = $zabbix->getHosts();
            if (!$hosts['success']) {
                throw new Exception($hosts['message']);
            }
            
            $statusCount = ['online' => 0, 'offline' => 0, 'unknown' => 0];
            foreach ($hosts['data'] as $host) {
                switch ($host['available']) {
                    case '1': $statusCount['online']++; break;
                    case '2': $statusCount['offline']++; break;
                    default: $statusCount['unknown']++; break;
                }
            }
            
            $data = [
                'type' => 'doughnut',
                'labels' => ['Online', 'Offline', 'Desconhecido'],
                'datasets' => [{
                    'data' => array_values($statusCount),
                    'backgroundColor' => ['#10b981', '#ef4444', '#6b7280'],
                    'borderWidth' => 2,
                    'borderColor' => '#ffffff'
                }]
            ];
            break;
            
        case 'cpu_usage':
            // Gráfico de uso de CPU por host
            $hosts = $zabbix->getHosts();
            if (!$hosts['success']) {
                throw new Exception($hosts['message']);
            }
            
            $labels = [];
            $cpuData = [];
            
            foreach (array_slice($hosts['data'], 0, 10) as $host) {
                $metrics = $zabbix->getHostMetrics($host['hostid']);
                if ($metrics['success'] && !empty($metrics['data']['cpu'])) {
                    $labels[] = $host['name'];
                    $cpuUsage = 0;
                    
                    foreach ($metrics['data']['cpu'] as $metric) {
                        if (strpos($metric['key'], 'util') !== false) {
                            $cpuUsage = max($cpuUsage, floatval($metric['value']));
                        }
                    }
                    
                    $cpuData[] = $cpuUsage;
                } else {
                    $labels[] = $host['name'];
                    $cpuData[] = 0;
                }
            }
            
            $data = [
                'type' => 'bar',
                'labels' => $labels,
                'datasets' => [{
                    'label' => 'Uso de CPU (%)',
                    'data' => $cpuData,
                    'backgroundColor' => '#ef4444',
                    'borderColor' => '#dc2626',
                    'borderWidth' => 1
                }]
            ];
            break;
            
        case 'memory_usage':
            // Gráfico de uso de memória por host
            $hosts = $zabbix->getHosts();
            if (!$hosts['success']) {
                throw new Exception($hosts['message']);
            }
            
            $labels = [];
            $memoryData = [];
            
            foreach (array_slice($hosts['data'], 0, 10) as $host) {
                $metrics = $zabbix->getHostMetrics($host['hostid']);
                if ($metrics['success'] && !empty($metrics['data']['memory'])) {
                    $labels[] = $host['name'];
                    $memoryUsage = 0;
                    
                    foreach ($metrics['data']['memory'] as $metric) {
                        if (strpos($metric['key'], 'util') !== false) {
                            $memoryUsage = max($memoryUsage, floatval($metric['value']));
                        }
                    }
                    
                    $memoryData[] = $memoryUsage;
                } else {
                    $labels[] = $host['name'];
                    $memoryData[] = 0;
                }
            }
            
            $data = [
                'type' => 'bar',
                'labels' => $labels,
                'datasets' => [{
                    'label' => 'Uso de Memória (%)',
                    'data' => $memoryData,
                    'backgroundColor' => '#3b82f6',
                    'borderColor' => '#2563eb',
                    'borderWidth' => 1
                }]
            ];
            break;
            
        case 'problems_severity':
            // Gráfico de problemas por severidade
            $problems = $zabbix->getProblems();
            if (!$problems['success']) {
                throw new Exception($problems['message']);
            }
            
            $severityCount = [0, 0, 0, 0, 0, 0]; // 0-5
            foreach ($problems['data'] as $problem) {
                $severity = intval($problem['severity']);
                $severityCount[$severity]++;
            }
            
            $data = [
                'type' => 'bar',
                'labels' => ['Não classificado', 'Informação', 'Atenção', 'Média', 'Alta', 'Desastre'],
                'datasets' => [{
                    'label' => 'Número de Problemas',
                    'data' => $severityCount,
                    'backgroundColor' => [
                        '#6b7280', '#3b82f6', '#f59e0b', '#f59e0b', '#ef4444', '#dc2626'
                    ],
                    'borderWidth' => 1
                }]
            ];
            break;
            
        case 'host_history':
            // Histórico de métricas de um host específico
            if (!$hostId) {
                throw new Exception('Host ID é obrigatório para histórico');
            }
            
            $metrics = $zabbix->getHostMetrics($hostId);
            if (!$metrics['success']) {
                throw new Exception($metrics['message']);
            }
            
            // Pegar primeiro item de CPU para histórico
            $cpuItem = null;
            foreach ($metrics['data']['cpu'] as $metric) {
                if (strpos($metric['key'], 'util') !== false) {
                    // Buscar itemid
                    $items = $zabbix->getHostItems($hostId, [$metric['key']]);
                    if ($items['success'] && !empty($items['data'])) {
                        $cpuItem = $items['data'][0]['itemid'];
                        break;
                    }
                }
            }
            
            if ($cpuItem) {
                $history = $zabbix->getHistory($cpuItem, $timeFrom, $timeTo, 50);
                if ($history['success']) {
                    $labels = [];
                    $values = [];
                    
                    foreach (array_reverse($history['data']) as $point) {
                        $labels[] = date('H:i', $point['clock']);
                        $values[] = floatval($point['value']);
                    }
                    
                    $data = [
                        'type' => 'line',
                        'labels' => $labels,
                        'datasets' => [{
                            'label' => 'Uso de CPU (%)',
                            'data' => $values,
                            'borderColor' => '#ef4444',
                            'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                            'fill' => true,
                            'tension' => 0.4
                        }]
                    ];
                } else {
                    throw new Exception('Erro ao obter histórico');
                }
            } else {
                throw new Exception('Item de CPU não encontrado');
            }
            break;
            
        default:
            throw new Exception('Tipo de gráfico não suportado');
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data,
        'period' => $period,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Converter período em segundos
 */
function getPeriodSeconds($period) {
    switch ($period) {
        case '15m': return 900;
        case '30m': return 1800;
        case '1h': return 3600;
        case '3h': return 10800;
        case '6h': return 21600;
        case '12h': return 43200;
        case '24h': return 86400;
        case '7d': return 604800;
        default: return 3600; // 1 hora padrão
    }
}
?>
