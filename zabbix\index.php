<?php
/**
 * Dashboard Zabbix - Página Principal
 * Design inspirado na Megainffo
 */

require_once 'config/config.php';
require_once 'classes/ZabbixAPI.php';

// Inicializar API do Zabbix
$zabbix = new ZabbixAPI();

// Obter dados iniciais
$summary = $zabbix->getSystemSummary();
$testConnection = $zabbix->testConnection();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ZABBIX_DASHBOARD_TITLE; ?></title>
    <link rel="stylesheet" href="assets/css/zabbix-style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Dashboard Zabbix
                <span style="font-size: 0.8rem; opacity: 0.8;">Megainffo Style</span>
            </div>
            <nav class="nav">
                <a href="#" class="active">Dashboard</a>
                <a href="pages/hosts.php">Servidores</a>
                <a href="pages/problems.php">Problemas</a>
                <a href="pages/charts.php">Gráficos</a>
                <a href="pages/settings.php">Configurações</a>
            </nav>
        </div>
    </header>

    <!-- Container Principal -->
    <main class="container">
        <!-- Status da Conexão -->
        <div class="summary-card mb-3" style="text-align: left;">
            <h3>🔗 Status da Conexão</h3>
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <p><strong>Zabbix:</strong> <?php echo ZABBIX_URL; ?></p>
                    <p><strong>Status:</strong> 
                        <span class="<?php echo $testConnection['success'] ? 'status-ok' : 'status-critical'; ?>">
                            <?php echo $testConnection['success'] ? 'Conectado' : 'Erro'; ?>
                        </span>
                    </p>
                    <?php if ($testConnection['success']): ?>
                        <p><strong>Versão:</strong> <?php echo $testConnection['data']['version']; ?></p>
                    <?php endif; ?>
                </div>
                <div>
                    <button id="refresh-btn" class="btn btn-primary">
                        🔄 Atualizar
                    </button>
                </div>
            </div>
        </div>

        <!-- Resumo Geral -->
        <div class="summary-grid">
            <!-- Total de Servidores -->
            <div class="summary-card servers fade-in-up">
                <div class="summary-icon">🖥️</div>
                <div class="summary-value" id="total-hosts">
                    <?php echo $summary['success'] ? $summary['data']['total_hosts'] : '0'; ?>
                </div>
                <div class="summary-label">Total de Servidores</div>
            </div>

            <!-- Servidores Online -->
            <div class="summary-card ok fade-in-up">
                <div class="summary-icon">✅</div>
                <div class="summary-value" id="hosts-online">
                    <?php echo $summary['success'] ? $summary['data']['hosts_up'] : '0'; ?>
                </div>
                <div class="summary-label">Servidores Online</div>
            </div>

            <!-- Problemas Críticos -->
            <div class="summary-card problems fade-in-up">
                <div class="summary-icon">🚨</div>
                <div class="summary-value" id="critical-problems">
                    <?php echo $summary['success'] ? $summary['data']['critical_problems'] : '0'; ?>
                </div>
                <div class="summary-label">Problemas Críticos</div>
            </div>

            <!-- Avisos -->
            <div class="summary-card warnings fade-in-up">
                <div class="summary-icon">⚠️</div>
                <div class="summary-value" id="warning-problems">
                    <?php echo $summary['success'] ? $summary['data']['warning_problems'] : '0'; ?>
                </div>
                <div class="summary-label">Avisos</div>
            </div>
        </div>

        <!-- Grid de Servidores -->
        <div class="servers-section mb-3">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h2>🖥️ Servidores Monitorados</h2>
                <div>
                    <select id="group-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem;">
                        <option value="">Todos os grupos</option>
                    </select>
                    <button id="refresh-hosts" class="btn btn-primary">🔄 Atualizar</button>
                </div>
            </div>
            <div id="servers-grid" class="servers-grid">
                <!-- Servidores serão carregados via JavaScript -->
                <div class="server-card">
                    <div class="server-header">
                        <div class="server-name">Carregando...</div>
                        <div class="server-status status-unknown">...</div>
                    </div>
                    <div class="server-metrics">
                        <div class="metric">
                            <div class="metric-label">CPU</div>
                            <div class="metric-value">--%</div>
                            <div class="metric-bar">
                                <div class="metric-progress cpu" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Memória</div>
                            <div class="metric-value">--%</div>
                            <div class="metric-bar">
                                <div class="metric-progress memory" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Disco</div>
                            <div class="metric-value">--%</div>
                            <div class="metric-bar">
                                <div class="metric-progress disk" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">Rede</div>
                            <div class="metric-value">OK</div>
                            <div class="metric-info">Conectado</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="charts-section">
            <!-- Visão Geral -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">📊 Status dos Servidores</h3>
                </div>
                <div class="chart-wrapper">
                    <canvas id="overview-chart" width="400" height="300"></canvas>
                </div>
            </div>

            <!-- Uso de CPU -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">🔥 Uso de CPU por Servidor</h3>
                </div>
                <div class="chart-wrapper">
                    <canvas id="cpu-chart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Problemas Recentes -->
        <div class="problems-table">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3>🚨 Problemas Recentes</h3>
                <a href="pages/problems.php" class="btn btn-primary">Ver Todos</a>
            </div>
            <div id="problems-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Severidade</th>
                            <th>Problema</th>
                            <th>Servidor</th>
                            <th>Tempo</th>
                        </tr>
                    </thead>
                    <tbody id="problems-tbody">
                        <tr>
                            <td colspan="4" style="text-align: center; padding: 2rem;">
                                Carregando problemas...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Informações do Sistema -->
        <div class="summary-card mt-3">
            <h3>ℹ️ Informações do Sistema</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                <div>
                    <strong>Última Atualização:</strong><br>
                    <span id="last-update"><?php echo date('d/m/Y H:i:s'); ?></span>
                </div>
                <div>
                    <strong>Intervalo de Atualização:</strong><br>
                    <?php echo ZABBIX_REFRESH_INTERVAL; ?> segundos
                </div>
                <div>
                    <strong>Cache:</strong><br>
                    <?php echo ZABBIX_CACHE_ENABLED ? 'Habilitado' : 'Desabilitado'; ?>
                </div>
                <div>
                    <strong>Timezone:</strong><br>
                    <?php echo date_default_timezone_get(); ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/zabbix-dashboard.js"></script>
    
    <script>
        // Configurações específicas da página
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar dashboard
            window.zabbixDashboard = new ZabbixDashboard();
            
            // Carregar dados iniciais
            loadInitialData();
        });

        async function loadInitialData() {
            try {
                // Carregar resumo
                await window.zabbixDashboard.loadSummary();
                
                // Carregar servidores
                await window.zabbixDashboard.loadHosts();
                
                // Carregar gráficos
                await initCharts();
                
                // Carregar problemas
                await window.zabbixDashboard.loadProblems(5);
                
            } catch (error) {
                console.error('Erro ao carregar dados iniciais:', error);
                window.zabbixDashboard.showError('Erro ao carregar dados: ' + error.message);
            }
        }

        async function initCharts() {
            // Gráfico de visão geral
            const overviewData = await fetch('api/get_charts.php?type=overview');
            const overviewJson = await overviewData.json();
            
            if (overviewJson.success) {
                const ctx1 = document.getElementById('overview-chart').getContext('2d');
                new Chart(ctx1, {
                    type: overviewJson.data.type,
                    data: overviewJson.data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // Gráfico de CPU
            const cpuData = await fetch('api/get_charts.php?type=cpu_usage');
            const cpuJson = await cpuData.json();
            
            if (cpuJson.success) {
                const ctx2 = document.getElementById('cpu-chart').getContext('2d');
                new Chart(ctx2, {
                    type: cpuJson.data.type,
                    data: cpuJson.data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        }
    </script>
</body>
</html>
