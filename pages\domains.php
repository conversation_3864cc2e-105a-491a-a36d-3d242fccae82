<?php
/**
 * Dashboard Pi-hole - P<PERSON><PERSON><PERSON>
 */

require_once '../config/config.php';
require_once '../classes/PiholeAPI.php';

$pihole = new PiholeAPI();
$topDomains = $pihole->getTopDomains(20);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domínios - <?php echo DASHBOARD_TITLE; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Dashboard Pi-hole
                <span style="font-size: 0.8rem; opacity: 0.8;">Megainffo Style</span>
            </div>
            <nav class="nav">
                <a href="../index.php">Dashboard</a>
                <a href="logs.php">Logs</a>
                <a href="#" class="active">Domínios</a>
                <a href="clients.php">Clientes</a>
                <a href="settings.php">Configurações</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="page-header mb-3">
            <h1>Top Domínios</h1>
            <p>Domínios mais consultados e bloqueados</p>
        </div>

        <div class="domains-grid">
            <!-- Top Queries -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>🔝 Domínios Mais Consultados</h3>
                    <span class="badge"><?php echo $topDomains['success'] ? count($topDomains['data']['top_queries']) : 0; ?> domínios</span>
                </div>
                <div class="domains-list">
                    <?php if ($topDomains['success'] && !empty($topDomains['data']['top_queries'])): ?>
                        <?php foreach ($topDomains['data']['top_queries'] as $domain => $count): ?>
                            <div class="domain-item">
                                <div class="domain-info">
                                    <span class="domain-name"><?php echo htmlspecialchars($domain); ?></span>
                                    <span class="domain-count"><?php echo number_format($count); ?> queries</span>
                                </div>
                                <div class="domain-bar">
                                    <div class="domain-progress" style="width: <?php echo min(100, ($count / max(array_values($topDomains['data']['top_queries']))) * 100); ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-data">
                            <p>Nenhum dado disponível</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Top Blocked -->
            <div class="stat-card">
                <div class="stat-header">
                    <h3>🚫 Domínios Mais Bloqueados</h3>
                    <span class="badge badge-danger"><?php echo $topDomains['success'] ? count($topDomains['data']['top_ads']) : 0; ?> domínios</span>
                </div>
                <div class="domains-list">
                    <?php if ($topDomains['success'] && !empty($topDomains['data']['top_ads'])): ?>
                        <?php foreach ($topDomains['data']['top_ads'] as $domain => $count): ?>
                            <div class="domain-item blocked">
                                <div class="domain-info">
                                    <span class="domain-name"><?php echo htmlspecialchars($domain); ?></span>
                                    <span class="domain-count"><?php echo number_format($count); ?> bloqueios</span>
                                </div>
                                <div class="domain-bar">
                                    <div class="domain-progress blocked" style="width: <?php echo min(100, ($count / max(array_values($topDomains['data']['top_ads']))) * 100); ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-data">
                            <p>Nenhum domínio bloqueado</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Gráfico de Domínios -->
        <div class="chart-section mt-3">
            <div class="stat-card">
                <div class="stat-header">
                    <h3>📊 Distribuição de Queries por Domínio</h3>
                </div>
                <div class="chart-wrapper">
                    <canvas id="domains-chart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initDomainsChart();
        });

        async function initDomainsChart() {
            try {
                const response = await fetch('../api/get_charts.php?type=domains');
                const data = await response.json();
                
                if (data.success) {
                    const ctx = document.getElementById('domains-chart').getContext('2d');
                    new Chart(ctx, {
                        type: data.data.type,
                        data: data.data,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            indexAxis: 'y',
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                x: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Erro ao carregar gráfico:', error);
            }
        }
    </script>

    <style>
        .page-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .page-header p {
            color: var(--text-secondary);
            margin: 0;
        }
        
        .domains-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .badge {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .badge-danger {
            background: var(--danger-color);
        }
        
        .domains-list {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .domain-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s;
        }
        
        .domain-item:hover {
            background: var(--light-color);
        }
        
        .domain-item:last-child {
            border-bottom: none;
        }
        
        .domain-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .domain-name {
            font-weight: 600;
            color: var(--text-primary);
            word-break: break-all;
        }
        
        .domain-count {
            font-size: 0.9rem;
            color: var(--text-secondary);
            white-space: nowrap;
        }
        
        .domain-bar {
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .domain-progress {
            height: 100%;
            background: var(--success-color);
            transition: width 0.3s ease;
        }
        
        .domain-progress.blocked {
            background: var(--danger-color);
        }
        
        .no-data {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }
        
        .chart-wrapper {
            height: 400px;
            padding: 1rem;
        }
        
        @media (max-width: 768px) {
            .domains-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</body>
</html>
