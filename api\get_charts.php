<?php
/**
 * API Endpoint - Obter dados para gráficos
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/PiholeAPI.php';

try {
    // Criar instância da API
    $pihole = new PiholeAPI();
    
    // Obter tipo de gráfico solicitado
    $type = $_GET['type'] ?? 'overview';
    
    switch ($type) {
        case 'overview':
            // Dados gerais para gráfico de pizza
            $stats = $pihole->getStats();
            if (!$stats['success']) {
                throw new Exception($stats['message']);
            }
            
            $data = [
                'success' => true,
                'data' => [
                    'type' => 'pie',
                    'labels' => ['Queries Permitidas', 'Queries Bloqueadas'],
                    'datasets' => [{
                        'data' => [
                            $stats['data']['dns_queries_today'] - $stats['data']['ads_blocked_today'],
                            $stats['data']['ads_blocked_today']
                        ],
                        'backgroundColor' => ['#10b981', '#ef4444'],
                        'borderWidth' => 2,
                        'borderColor' => '#ffffff'
                    }]
                ]
            ];
            break;
            
        case 'history':
            // Histórico de queries (simulado para demonstração)
            $hours = [];
            $queries = [];
            $blocked = [];
            
            for ($i = 23; $i >= 0; $i--) {
                $hour = date('H:i', strtotime("-{$i} hours"));
                $hours[] = $hour;
                
                // Dados simulados - em produção, usar dados reais da API
                $totalQueries = rand(50, 200);
                $blockedQueries = rand(10, 50);
                
                $queries[] = $totalQueries;
                $blocked[] = $blockedQueries;
            }
            
            $data = [
                'success' => true,
                'data' => [
                    'type' => 'line',
                    'labels' => $hours,
                    'datasets' => [
                        [
                            'label' => 'Total de Queries',
                            'data' => $queries,
                            'borderColor' => '#3b82f6',
                            'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                            'fill' => true,
                            'tension' => 0.4
                        ],
                        [
                            'label' => 'Queries Bloqueadas',
                            'data' => $blocked,
                            'borderColor' => '#ef4444',
                            'backgroundColor' => 'rgba(239, 68, 68, 0.1)',
                            'fill' => true,
                            'tension' => 0.4
                        ]
                    ]
                ]
            ];
            break;
            
        case 'clients':
            // Top clients
            $clients = $pihole->getTopClients(10);
            if (!$clients['success']) {
                throw new Exception($clients['message']);
            }
            
            $labels = [];
            $values = [];
            
            foreach ($clients['data'] as $ip => $count) {
                $labels[] = $ip;
                $values[] = $count;
            }
            
            $data = [
                'success' => true,
                'data' => [
                    'type' => 'bar',
                    'labels' => $labels,
                    'datasets' => [{
                        'label' => 'Queries por Cliente',
                        'data' => $values,
                        'backgroundColor' => '#06b6d4',
                        'borderColor' => '#0891b2',
                        'borderWidth' => 1
                    }]
                ]
            ];
            break;
            
        case 'domains':
            // Top domains
            $domains = $pihole->getTopDomains(10);
            if (!$domains['success']) {
                throw new Exception($domains['message']);
            }
            
            $labels = [];
            $values = [];
            
            foreach ($domains['data']['top_queries'] as $domain => $count) {
                $labels[] = strlen($domain) > 20 ? substr($domain, 0, 20) . '...' : $domain;
                $values[] = $count;
            }
            
            $data = [
                'success' => true,
                'data' => [
                    'type' => 'horizontalBar',
                    'labels' => $labels,
                    'datasets' => [{
                        'label' => 'Queries por Domínio',
                        'data' => $values,
                        'backgroundColor' => '#10b981',
                        'borderColor' => '#059669',
                        'borderWidth' => 1
                    }]
                ]
            ];
            break;
            
        default:
            throw new Exception('Tipo de gráfico não suportado');
    }
    
    echo json_encode($data);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
