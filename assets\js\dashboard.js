/**
 * Dashboard Pi-hole - JavaScript
 * Funcionalidades interativas e atualizações em tempo real
 */

class PiholeDashboard {
    constructor() {
        this.refreshInterval = 30000; // 30 segundos
        this.charts = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadData();
        this.startAutoRefresh();
        this.addAnimations();
    }

    setupEventListeners() {
        // Botão de refresh manual
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadData());
        }

        // Botões de controle do Pi-hole
        const enableBtn = document.getElementById('enable-btn');
        const disableBtn = document.getElementById('disable-btn');
        
        if (enableBtn) {
            enableBtn.addEventListener('click', () => this.togglePihole('enable'));
        }
        
        if (disableBtn) {
            disableBtn.addEventListener('click', () => this.togglePihole('disable'));
        }

        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('auto-refresh');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }
    }

    async loadData() {
        try {
            this.showLoading();
            
            const response = await fetch('api/get_stats.php');
            const data = await response.json();
            
            if (data.success) {
                this.updateStats(data.data);
                this.updateCharts(data.data);
                this.updateLastUpdate();
            } else {
                this.showError('Erro ao carregar dados: ' + data.message);
            }
        } catch (error) {
            this.showError('Erro de conexão: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    updateStats(data) {
        // Atualizar cards de estatísticas
        this.updateElement('total-queries', this.formatNumber(data.dns_queries_today));
        this.updateElement('blocked-queries', this.formatNumber(data.ads_blocked_today));
        this.updateElement('block-percentage', data.ads_percentage_today + '%');
        this.updateElement('unique-domains', this.formatNumber(data.unique_domains));
        this.updateElement('unique-clients', this.formatNumber(data.unique_clients));
        
        // Status do Pi-hole
        const statusElement = document.getElementById('pihole-status');
        if (statusElement) {
            statusElement.textContent = data.status === 'enabled' ? 'Ativo' : 'Inativo';
            statusElement.className = data.status === 'enabled' ? 'status-enabled' : 'status-disabled';
        }

        // Atualizar indicadores visuais
        this.updateStatusIndicators(data);
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            element.classList.add('fade-in-up');
            setTimeout(() => element.classList.remove('fade-in-up'), 600);
        }
    }

    updateStatusIndicators(data) {
        // Atualizar cores dos cards baseado no status
        const cards = document.querySelectorAll('.stat-card');
        cards.forEach(card => {
            card.style.borderLeftColor = data.status === 'enabled' ? 'var(--success-color)' : 'var(--danger-color)';
        });
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    async togglePihole(action) {
        try {
            const response = await fetch('api/toggle_pihole.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: action })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess(`Pi-hole ${action === 'enable' ? 'ativado' : 'desativado'} com sucesso!`);
                setTimeout(() => this.loadData(), 2000);
            } else {
                this.showError('Erro: ' + data.message);
            }
        } catch (error) {
            this.showError('Erro de conexão: ' + error.message);
        }
    }

    startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshTimer = setInterval(() => this.loadData(), this.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    updateLastUpdate() {
        const element = document.getElementById('last-update');
        if (element) {
            const now = new Date();
            element.textContent = `Última atualização: ${now.toLocaleTimeString('pt-BR')}`;
        }
    }

    showLoading() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<span class="loading"></span> Carregando...';
            refreshBtn.disabled = true;
        }
    }

    hideLoading() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.innerHTML = '🔄 Atualizar';
            refreshBtn.disabled = false;
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Adicionar estilos inline
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
            background: ${type === 'error' ? 'var(--danger-color)' : 
                        type === 'success' ? 'var(--success-color)' : 
                        'var(--primary-color)'};
        `;
        
        document.body.appendChild(notification);
        
        // Remover após 5 segundos
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    addAnimations() {
        // Adicionar animações CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
}

// Inicializar dashboard quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new PiholeDashboard();
});

// Utilitários globais
window.utils = {
    formatBytes: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    formatUptime: function(seconds) {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (days > 0) return `${days}d ${hours}h ${minutes}m`;
        if (hours > 0) return `${hours}h ${minutes}m`;
        return `${minutes}m`;
    }
};
