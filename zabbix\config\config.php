<?php
/**
 * Configurações do Dashboard Zabbix
 * Inspirado no design da Megainffo
 */

// Configurações do Zabbix
define('ZABBIX_URL', 'http://192.168.15.35/zabbix/');
define('ZABBIX_API_URL', ZABBIX_URL . 'api_jsonrpc.php');
define('ZABBIX_API_TOKEN', '9fd2877a0701b9ba124035b9569664a70a88319df28c6b987e1611894493fd39');

// Configurações do Dashboard
define('ZABBIX_DASHBOARD_TITLE', 'Dashboard Zabbix - Megainffo Style');
define('ZABBIX_REFRESH_INTERVAL', 30); // segundos

// Configurações de cache
define('ZABBIX_CACHE_ENABLED', true);
define('ZABBIX_CACHE_DURATION', 60); // segundos

// Configurações de autenticação (opcional)
define('ZABBIX_ENABLE_AUTH', false);
define('ZABBIX_ADMIN_PASSWORD', 'admin123');

// Timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de erro
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Items principais para monitoramento
define('ZABBIX_MAIN_ITEMS', [
    'cpu' => [
        'system.cpu.util',
        'system.cpu.load[percpu,avg1]',
        'proc.cpu.util[]'
    ],
    'memory' => [
        'vm.memory.util',
        'vm.memory.size[available]',
        'vm.memory.size[total]',
        'system.swap.size[,pfree]'
    ],
    'disk' => [
        'vfs.fs.size[/,pused]',
        'vfs.fs.size[/,used]',
        'vfs.fs.size[/,total]',
        'system.disk.util'
    ],
    'network' => [
        'net.if.in[eth0]',
        'net.if.out[eth0]',
        'icmpping'
    ]
]);

// Grupos de hosts para filtrar
define('ZABBIX_HOST_GROUPS', [
    'Linux servers',
    'Windows servers',
    'Zabbix servers',
    'Templates'
]);

// Configurações de cores para gráficos
define('ZABBIX_COLORS', [
    'cpu' => '#ef4444',
    'memory' => '#3b82f6',
    'disk' => '#10b981',
    'network' => '#f59e0b',
    'ok' => '#10b981',
    'warning' => '#f59e0b',
    'critical' => '#ef4444',
    'unknown' => '#6b7280'
]);

// Limites para alertas
define('ZABBIX_THRESHOLDS', [
    'cpu' => ['warning' => 70, 'critical' => 90],
    'memory' => ['warning' => 80, 'critical' => 95],
    'disk' => ['warning' => 80, 'critical' => 95]
]);
?>
