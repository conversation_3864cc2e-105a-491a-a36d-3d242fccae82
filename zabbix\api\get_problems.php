<?php
/**
 * API Endpoint - Obter problemas ativos
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/ZabbixAPI.php';

try {
    // Criar instância da API
    $zabbix = new ZabbixAPI();
    
    // Obter parâmetros
    $hostId = $_GET['host_id'] ?? null;
    $severity = $_GET['severity'] ?? null;
    $limit = $_GET['limit'] ?? 50;
    
    // Obter problemas
    $problems = $zabbix->getProblems(
        $hostId ? [$hostId] : null,
        $severity ? [$severity] : null
    );
    
    if (!$problems['success']) {
        throw new Exception($problems['message']);
    }
    
    $problemsData = [];
    
    foreach (array_slice($problems['data'], 0, $limit) as $problem) {
        $severity = intval($problem['severity']);
        
        // Mapear severidade
        $severityMap = [
            0 => ['text' => 'Não classificado', 'class' => 'info'],
            1 => ['text' => 'Informação', 'class' => 'info'],
            2 => ['text' => 'Atenção', 'class' => 'warning'],
            3 => ['text' => 'Média', 'class' => 'warning'],
            4 => ['text' => 'Alta', 'class' => 'critical'],
            5 => ['text' => 'Desastre', 'class' => 'critical']
        ];
        
        $severityInfo = $severityMap[$severity] ?? $severityMap[0];
        
        $problemData = [
            'eventid' => $problem['eventid'],
            'name' => $problem['name'],
            'severity' => $severity,
            'severity_text' => $severityInfo['text'],
            'severity_class' => $severityInfo['class'],
            'clock' => $problem['clock'],
            'time' => date('Y-m-d H:i:s', $problem['clock']),
            'time_ago' => timeAgo($problem['clock']),
            'hosts' => []
        ];
        
        // Adicionar informações dos hosts
        if (isset($problem['hosts'])) {
            foreach ($problem['hosts'] as $host) {
                $problemData['hosts'][] = [
                    'hostid' => $host['hostid'],
                    'hostname' => $host['host'],
                    'name' => $host['name']
                ];
            }
        }
        
        // Adicionar informações do trigger
        if (isset($problem['triggers'][0])) {
            $trigger = $problem['triggers'][0];
            $problemData['trigger'] = [
                'triggerid' => $trigger['triggerid'],
                'description' => $trigger['description'],
                'priority' => $trigger['priority']
            ];
        }
        
        $problemsData[] = $problemData;
    }
    
    // Estatísticas dos problemas
    $stats = [
        'total' => count($problems['data']),
        'critical' => 0,
        'warning' => 0,
        'info' => 0
    ];
    
    foreach ($problems['data'] as $problem) {
        $severity = intval($problem['severity']);
        if ($severity >= 4) {
            $stats['critical']++;
        } elseif ($severity >= 2) {
            $stats['warning']++;
        } else {
            $stats['info']++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => $problemsData,
        'stats' => $stats,
        'total' => count($problemsData),
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => 'API_ERROR'
    ]);
}

/**
 * Calcular tempo decorrido
 */
function timeAgo($timestamp) {
    $diff = time() - $timestamp;
    
    if ($diff < 60) {
        return $diff . ' segundos atrás';
    } elseif ($diff < 3600) {
        return floor($diff / 60) . ' minutos atrás';
    } elseif ($diff < 86400) {
        return floor($diff / 3600) . ' horas atrás';
    } else {
        return floor($diff / 86400) . ' dias atrás';
    }
}
?>
