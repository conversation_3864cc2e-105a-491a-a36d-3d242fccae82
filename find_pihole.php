<?php
/**
 * Script para Encontrar Pi-hole na Rede
 * Testa diferentes IPs e portas para localizar o Pi-hole
 */

header('Content-Type: text/html; charset=UTF-8');

// Função para testar conectividade
function testPiholeConnection($ip, $port = 80) {
    $socket = @fsockopen($ip, $port, $errno, $errstr, 3);
    if (!$socket) {
        return false;
    }
    fclose($socket);
    
    // Testar se é realmente um Pi-hole
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET'
        ]
    ]);
    
    $test_url = "http://{$ip}:{$port}/admin/api.php?summary";
    $response = @file_get_contents($test_url, false, $context);
    
    if ($response) {
        $data = json_decode($response, true);
        if ($data && isset($data['dns_queries_today'])) {
            return [
                'url' => $test_url,
                'data' => $data
            ];
        }
    }
    
    return false;
}

// IPs comuns para testar
$common_ips = [
    '************',  // IP configurado
    '***********',   // Gateway comum
    '***********',   // Gateway comum
    '***********00', // IP comum Pi-hole
    '***********01',
    '***********02',
    '*************',
    '***********00',
    '***********01',
    '***********02',
    '********',      // Rede 10.x
    '********00',
    '**********',    // Rede 172.16.x
    '************'
];

// Portas comuns para testar
$common_ports = [80, 8080, 8081, 443, 4711];

?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Encontrar Pi-hole na Rede</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #059669, #10b981);
            color: white;
            border-radius: 1rem;
        }
        .search-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .found {
            background: #ecfdf5;
            border: 2px solid #10b981;
            color: #065f46;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem 0;
        }
        .testing {
            background: #fef3c7;
            border-left: 4px solid #d97706;
            color: #92400e;
            padding: 0.75rem;
            margin: 0.25rem 0;
        }
        .not-found {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            color: #991b1b;
            padding: 0.75rem;
            margin: 0.25rem 0;
        }
        .config-box {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: monospace;
            margin: 1rem 0;
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #10b981);
            transition: width 0.3s ease;
        }
        pre {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Encontrar Pi-hole na Rede</h1>
        <p>Escaneando IPs e portas comuns para localizar seu Pi-hole</p>
    </div>

    <div class="search-section">
        <h3>🌐 Resultados da Busca</h3>
        <div id="progress-container">
            <div class="progress">
                <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
            </div>
            <div id="progress-text">Iniciando busca...</div>
        </div>
        <div id="results"></div>
    </div>

    <div class="search-section">
        <h3>📝 Instruções</h3>
        <ol>
            <li>Este script testará IPs e portas comuns para encontrar seu Pi-hole</li>
            <li>Quando encontrado, você verá as configurações corretas</li>
            <li>Copie as configurações para o arquivo config/config.php</li>
            <li>Se não encontrar automaticamente, tente inserir o IP manualmente abaixo</li>
        </ol>
    </div>

    <div class="search-section">
        <h3>🔧 Teste Manual</h3>
        <form method="GET" style="margin: 1rem 0;">
            <label>IP do Pi-hole:</label>
            <input type="text" name="manual_ip" placeholder="***********00" style="padding: 0.5rem; margin: 0 0.5rem;">
            <label>Porta:</label>
            <input type="number" name="manual_port" placeholder="80" value="80" style="padding: 0.5rem; margin: 0 0.5rem; width: 80px;">
            <button type="submit" class="btn">Testar</button>
        </form>
        
        <?php
        if (isset($_GET['manual_ip']) && $_GET['manual_ip']) {
            $manual_ip = $_GET['manual_ip'];
            $manual_port = $_GET['manual_port'] ?: 80;
            
            echo "<div class='testing'>Testando {$manual_ip}:{$manual_port}...</div>";
            
            $result = testPiholeConnection($manual_ip, $manual_port);
            if ($result) {
                echo "<div class='found'>";
                echo "<h4>✅ Pi-hole encontrado!</h4>";
                echo "<p><strong>IP:</strong> {$manual_ip}</p>";
                echo "<p><strong>Porta:</strong> {$manual_port}</p>";
                echo "<p><strong>URL da API:</strong> {$result['url']}</p>";
                echo "<div class='config-box'>";
                echo "define('PIHOLE_IP', '{$manual_ip}');\n";
                echo "define('PIHOLE_PORT', '{$manual_port}');\n";
                echo "define('PIHOLE_API_URL', 'http://{$manual_ip}:{$manual_port}/admin/api.php');";
                echo "</div>";
                echo "<pre>" . json_encode($result['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                echo "</div>";
            } else {
                echo "<div class='not-found'>❌ Pi-hole não encontrado em {$manual_ip}:{$manual_port}</div>";
            }
        }
        ?>
    </div>

    <script>
        // Busca automática
        const ips = <?php echo json_encode($common_ips); ?>;
        const ports = <?php echo json_encode($common_ports); ?>;
        const resultsDiv = document.getElementById('results');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');
        
        let totalTests = ips.length * ports.length;
        let currentTest = 0;
        let found = false;

        async function testConnection(ip, port) {
            try {
                const response = await fetch(`test_pihole_connection.php?ip=${ip}&port=${port}`);
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function searchPihole() {
            progressText.textContent = `Testando ${totalTests} combinações de IP:Porta...`;

            for (let ip of ips) {
                for (let port of ports) {
                    currentTest++;
                    const progress = (currentTest / totalTests) * 100;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = `Testando ${ip}:${port} (${currentTest}/${totalTests})`;

                    // Adicionar resultado de teste
                    const testDiv = document.createElement('div');
                    testDiv.className = 'testing';
                    testDiv.textContent = `Testando ${ip}:${port}...`;
                    resultsDiv.appendChild(testDiv);

                    // Fazer teste real
                    const result = await testConnection(ip, port);

                    // Remover div de teste
                    resultsDiv.removeChild(testDiv);

                    if (result.success) {
                        // Pi-hole encontrado!
                        const foundDiv = document.createElement('div');
                        foundDiv.className = 'found';
                        foundDiv.innerHTML = `
                            <h4>✅ Pi-hole encontrado!</h4>
                            <p><strong>IP:</strong> ${result.ip}</p>
                            <p><strong>Porta:</strong> ${result.port}</p>
                            <p><strong>URL da API:</strong> ${result.url}</p>
                            <div class="config-box">
define('PIHOLE_IP', '${result.ip}');
define('PIHOLE_PORT', '${result.port}');
define('PIHOLE_API_URL', '${result.config.PIHOLE_API_URL}');
                            </div>
                            <p><strong>Estatísticas atuais:</strong></p>
                            <pre>${JSON.stringify(result.data, null, 2)}</pre>
                            <p>✅ Copie as configurações acima para config/config.php</p>
                        `;
                        resultsDiv.appendChild(foundDiv);
                        found = true;
                        break;
                    } else {
                        // Mostrar erro apenas se for um erro interessante
                        if (result.error && !result.error.includes('Porta não acessível')) {
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'not-found';
                            errorDiv.textContent = `${ip}:${port} - ${result.error}`;
                            resultsDiv.appendChild(errorDiv);
                        }
                    }

                    // Pequena pausa para não sobrecarregar
                    await new Promise(resolve => setTimeout(resolve, 50));
                }
                if (found) break;
            }
            
            if (!found) {
                progressText.textContent = 'Busca concluída - Pi-hole não encontrado automaticamente';
                const notFoundDiv = document.createElement('div');
                notFoundDiv.className = 'not-found';
                notFoundDiv.innerHTML = `
                    <h4>❌ Pi-hole não encontrado automaticamente</h4>
                    <p>Tente:</p>
                    <ul>
                        <li>Verificar se o Pi-hole está ligado</li>
                        <li>Confirmar o IP correto na sua rede</li>
                        <li>Usar o teste manual acima</li>
                        <li>Verificar se está na mesma rede</li>
                    </ul>
                `;
                resultsDiv.appendChild(notFoundDiv);
            } else {
                progressText.textContent = 'Pi-hole encontrado com sucesso!';
            }
        }

        // Iniciar busca automaticamente
        searchPihole();
    </script>
</body>
</html>
