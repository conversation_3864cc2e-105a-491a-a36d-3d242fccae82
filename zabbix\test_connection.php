<?php
/**
 * Script de Teste de Conectividade com Zabbix
 * Execute este arquivo para verificar se a conexão está funcionando
 */

require_once 'config/config.php';
require_once 'classes/ZabbixAPI.php';

// Configurar cabeçalho para exibição no navegador
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Conectividade - Dashboard Zabbix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8fafc;
            color: #1f2937;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #1e3a8a, #3b82f6);
            color: white;
            border-radius: 1rem;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .test-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #1e3a8a;
        }
        .success {
            color: #10b981;
            background: #ecfdf5;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 4px solid #10b981;
        }
        .error {
            color: #ef4444;
            background: #fef2f2;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 4px solid #ef4444;
        }
        .info {
            color: #3b82f6;
            background: #eff6ff;
            padding: 0.75rem;
            border-radius: 0.5rem;
            border-left: 4px solid #3b82f6;
        }
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .config-table th,
        .config-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .config-table th {
            background: #f8fafc;
            font-weight: 600;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #1e3a8a;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        pre {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Teste de Conectividade</h1>
        <p>Dashboard Zabbix - Megainffo Style</p>
    </div>

    <?php
    echo '<div class="test-section">';
    echo '<div class="test-title">📋 Configurações Atuais</div>';
    echo '<table class="config-table">';
    echo '<tr><th>Configuração</th><th>Valor</th></tr>';
    echo '<tr><td>URL do Zabbix</td><td>' . ZABBIX_URL . '</td></tr>';
    echo '<tr><td>URL da API</td><td>' . ZABBIX_API_URL . '</td></tr>';
    echo '<tr><td>Token da API</td><td>' . (ZABBIX_API_TOKEN ? substr(ZABBIX_API_TOKEN, 0, 10) . '...' : 'Não configurado') . '</td></tr>';
    echo '<tr><td>Cache Habilitado</td><td>' . (ZABBIX_CACHE_ENABLED ? 'Sim' : 'Não') . '</td></tr>';
    echo '<tr><td>Duração do Cache</td><td>' . ZABBIX_CACHE_DURATION . ' segundos</td></tr>';
    echo '<tr><td>Intervalo de Atualização</td><td>' . ZABBIX_REFRESH_INTERVAL . ' segundos</td></tr>';
    echo '</table>';
    echo '</div>';

    // Teste 1: Verificar se o Zabbix está acessível
    echo '<div class="test-section">';
    echo '<div class="test-title">🌐 Teste 1: Conectividade HTTP</div>';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents(ZABBIX_URL, false, $context);
    if ($response !== false) {
        echo '<div class="success">✅ Sucesso: Zabbix está acessível em ' . ZABBIX_URL . '</div>';
        echo '<div class="info">Tamanho da resposta: ' . strlen($response) . ' bytes</div>';
    } else {
        echo '<div class="error">❌ Erro: Não foi possível acessar o Zabbix</div>';
        echo '<div class="info">💡 Verifique se a URL está correta e se o Zabbix está funcionando.</div>';
    }
    echo '</div>';

    // Teste 2: Testar API do Zabbix
    echo '<div class="test-section">';
    echo '<div class="test-title">🔌 Teste 2: API do Zabbix</div>';
    
    try {
        $zabbix = new ZabbixAPI();
        $connection_test = $zabbix->testConnection();
        
        if ($connection_test['success']) {
            echo '<div class="success">✅ Sucesso: API do Zabbix está respondendo</div>';
            echo '<div class="info">Versão: ' . $connection_test['data']['version'] . '</div>';
        } else {
            echo '<div class="error">❌ Erro na API: ' . $connection_test['message'] . '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Teste 3: Obter hosts
    echo '<div class="test-section">';
    echo '<div class="test-title">🖥️ Teste 3: Hosts do Zabbix</div>';
    
    try {
        $zabbix = new ZabbixAPI();
        $hosts = $zabbix->getHosts();
        
        if ($hosts['success']) {
            echo '<div class="success">✅ Sucesso: ' . count($hosts['data']) . ' hosts encontrados</div>';
            
            if (!empty($hosts['data'])) {
                echo '<div class="info">Primeiros 5 hosts:</div>';
                echo '<pre>';
                foreach (array_slice($hosts['data'], 0, 5) as $host) {
                    echo "- {$host['name']} ({$host['host']}) - Status: {$host['status']}\n";
                }
                echo '</pre>';
            }
        } else {
            echo '<div class="error">❌ Erro ao obter hosts: ' . $hosts['message'] . '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Teste 4: Obter resumo do sistema
    echo '<div class="test-section">';
    echo '<div class="test-title">📊 Teste 4: Resumo do Sistema</div>';
    
    try {
        $zabbix = new ZabbixAPI();
        $summary = $zabbix->getSystemSummary();
        
        if ($summary['success']) {
            echo '<div class="success">✅ Sucesso: Resumo obtido com sucesso</div>';
            echo '<pre>' . json_encode($summary['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        } else {
            echo '<div class="error">❌ Erro ao obter resumo: ' . $summary['message'] . '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Teste 5: Verificar problemas
    echo '<div class="test-section">';
    echo '<div class="test-title">🚨 Teste 5: Problemas Ativos</div>';
    
    try {
        $zabbix = new ZabbixAPI();
        $problems = $zabbix->getProblems();
        
        if ($problems['success']) {
            echo '<div class="success">✅ Sucesso: ' . count($problems['data']) . ' problemas encontrados</div>';
            
            if (!empty($problems['data'])) {
                echo '<div class="info">Primeiros 3 problemas:</div>';
                echo '<pre>';
                foreach (array_slice($problems['data'], 0, 3) as $problem) {
                    echo "- {$problem['name']} (Severidade: {$problem['severity']})\n";
                }
                echo '</pre>';
            } else {
                echo '<div class="info">🎉 Nenhum problema ativo - Sistema funcionando normalmente!</div>';
            }
        } else {
            echo '<div class="error">❌ Erro ao obter problemas: ' . $problems['message'] . '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">❌ Exceção: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';

    // Informações do sistema
    echo '<div class="test-section">';
    echo '<div class="test-title">ℹ️ Informações do Sistema</div>';
    echo '<table class="config-table">';
    echo '<tr><th>Item</th><th>Valor</th></tr>';
    echo '<tr><td>Versão do PHP</td><td>' . phpversion() . '</td></tr>';
    echo '<tr><td>Servidor Web</td><td>' . $_SERVER['SERVER_SOFTWARE'] . '</td></tr>';
    echo '<tr><td>Data/Hora</td><td>' . date('Y-m-d H:i:s') . '</td></tr>';
    echo '<tr><td>Timezone</td><td>' . date_default_timezone_get() . '</td></tr>';
    echo '<tr><td>Extensão JSON</td><td>' . (extension_loaded('json') ? 'Disponível' : 'Não disponível') . '</td></tr>';
    echo '<tr><td>Extensão cURL</td><td>' . (extension_loaded('curl') ? 'Disponível' : 'Não disponível') . '</td></tr>';
    echo '</table>';
    echo '</div>';
    ?>

    <div class="test-section">
        <div class="test-title">🚀 Próximos Passos</div>
        <div class="info">
            Se todos os testes passaram, você pode acessar o dashboard principal:
            <br><br>
            <a href="index.php" class="btn">🏠 Ir para o Dashboard</a>
        </div>
        
        <?php if (!$connection_test['success'] ?? false): ?>
        <div class="error">
            <strong>Problemas encontrados:</strong><br>
            1. Verifique se o Zabbix está funcionando<br>
            2. Confirme a URL em config/config.php<br>
            3. Verifique se o token da API está correto<br>
            4. Teste o acesso manual ao Zabbix
        </div>
        <?php endif; ?>
    </div>

    <div class="test-section">
        <div class="test-title">📚 Documentação</div>
        <div class="info">
            Para mais informações, consulte a documentação do Zabbix API em:
            <br>
            <a href="https://www.zabbix.com/documentation/current/manual/api" target="_blank">
                https://www.zabbix.com/documentation/current/manual/api
            </a>
        </div>
    </div>
</body>
</html>
