<?php
/**
 * Dashboard Pi-hole - Página de Clientes
 */

require_once '../config/config.php';
require_once '../classes/PiholeAPI.php';

$pihole = new PiholeAPI();
$topClients = $pihole->getTopClients(20);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clientes - <?php echo DASHBOARD_TITLE; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Dashboard Pi-hole
                <span style="font-size: 0.8rem; opacity: 0.8;">Megainffo Style</span>
            </div>
            <nav class="nav">
                <a href="../index.php">Dashboard</a>
                <a href="logs.php">Logs</a>
                <a href="domains.php">Domínios</a>
                <a href="#" class="active">Clientes</a>
                <a href="settings.php">Configurações</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="page-header mb-3">
            <h1>Clientes da Rede</h1>
            <p>Dispositivos conectados e suas atividades</p>
        </div>

        <!-- Estatísticas dos Clientes -->
        <div class="clients-stats mb-3">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">Total de Clientes</span>
                    <span class="stat-icon">👥</span>
                </div>
                <div class="stat-value" style="color: var(--primary-color);">
                    <?php echo $topClients['success'] ? count($topClients['data']) : 0; ?>
                </div>
                <div class="stat-description">Dispositivos ativos hoje</div>
            </div>
        </div>

        <!-- Lista de Clientes -->
        <div class="clients-section">
            <div class="stat-card">
                <div class="stat-header">
                    <h3>👥 Top Clientes por Atividade</h3>
                    <div class="view-controls">
                        <button class="btn btn-primary" onclick="refreshClients()">🔄 Atualizar</button>
                    </div>
                </div>
                <div class="clients-table-container">
                    <table class="clients-table">
                        <thead>
                            <tr>
                                <th>Posição</th>
                                <th>Endereço IP</th>
                                <th>Hostname</th>
                                <th>Queries</th>
                                <th>Atividade</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($topClients['success'] && !empty($topClients['data'])): ?>
                                <?php 
                                $position = 1;
                                $maxQueries = max(array_values($topClients['data']));
                                foreach ($topClients['data'] as $ip => $queries): 
                                    $percentage = $maxQueries > 0 ? ($queries / $maxQueries) * 100 : 0;
                                    $hostname = gethostbyaddr($ip);
                                    if ($hostname === $ip) $hostname = 'Desconhecido';
                                ?>
                                    <tr class="client-row">
                                        <td>
                                            <span class="position-badge"><?php echo $position; ?></span>
                                        </td>
                                        <td>
                                            <div class="client-ip">
                                                <span class="ip-address"><?php echo htmlspecialchars($ip); ?></span>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="client-hostname">
                                                <?php echo htmlspecialchars($hostname); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="queries-count">
                                                <strong><?php echo number_format($queries); ?></strong>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="activity-bar">
                                                <div class="activity-progress" style="width: <?php echo $percentage; ?>%"></div>
                                            </div>
                                            <span class="activity-percentage"><?php echo round($percentage, 1); ?>%</span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-active">Ativo</span>
                                        </td>
                                    </tr>
                                <?php 
                                $position++;
                                endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="6" class="no-data">
                                        <p>Nenhum cliente encontrado</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Gráfico de Clientes -->
        <div class="chart-section mt-3">
            <div class="stat-card">
                <div class="stat-header">
                    <h3>📊 Distribuição de Queries por Cliente</h3>
                </div>
                <div class="chart-wrapper">
                    <canvas id="clients-chart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initClientsChart();
        });

        async function initClientsChart() {
            try {
                const response = await fetch('../api/get_charts.php?type=clients');
                const data = await response.json();
                
                if (data.success) {
                    const ctx = document.getElementById('clients-chart').getContext('2d');
                    new Chart(ctx, {
                        type: data.data.type,
                        data: data.data,
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                }
            } catch (error) {
                console.error('Erro ao carregar gráfico:', error);
            }
        }

        function refreshClients() {
            location.reload();
        }
    </script>

    <style>
        .page-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .page-header p {
            color: var(--text-secondary);
            margin: 0;
        }
        
        .clients-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .view-controls {
            display: flex;
            gap: 0.5rem;
        }
        
        .clients-table-container {
            overflow-x: auto;
        }
        
        .clients-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .clients-table th,
        .clients-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .clients-table th {
            background: var(--light-color);
            font-weight: 600;
            color: var(--text-primary);
            position: sticky;
            top: 0;
        }
        
        .client-row:hover {
            background: var(--light-color);
        }
        
        .position-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .client-ip .ip-address {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .client-hostname {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .queries-count strong {
            color: var(--text-primary);
        }
        
        .activity-bar {
            width: 100px;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.25rem;
        }
        
        .activity-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), var(--accent-color));
            transition: width 0.3s ease;
        }
        
        .activity-percentage {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active {
            background: var(--success-color);
            color: white;
        }
        
        .status-inactive {
            background: var(--text-secondary);
            color: white;
        }
        
        .no-data {
            text-align: center;
            padding: 2rem;
            color: var(--text-secondary);
        }
        
        .chart-wrapper {
            height: 400px;
            padding: 1rem;
        }
        
        @media (max-width: 768px) {
            .clients-table th,
            .clients-table td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
            
            .activity-bar {
                width: 60px;
            }
        }
    </style>
</body>
</html>
