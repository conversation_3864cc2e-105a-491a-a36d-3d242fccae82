# Dashboard Pi-hole - Est<PERSON>ffo

Um dashboard completo e personalizado para Pi-hole com design inspirado no site da Megainffo, desenvolvido em PHP, HTML e CSS.

## 🚀 Características

- **Design Moderno**: Interface inspirada no site da Megainffo com cores e tipografia profissionais
- **Responsivo**: Funciona perfeitamente em desktop, tablet e mobile
- **Tempo Real**: Atualização automática das estatísticas a cada 30 segundos
- **Gráficos Interativos**: Visualizações com Chart.js para melhor análise dos dados
- **API Completa**: Comunicação eficiente com a API do Pi-hole
- **Cache Inteligente**: Sistema de cache para melhor performance
- **Controles Administrativos**: Habilitar/desabilitar Pi-hole diretamente do dashboard

## 📊 Funcionalidades

### Dashboard Principal
- Estatísticas em tempo real (queries, bloqueios, percentuais)
- Status do sistema e controles de ativação
- Gráfico de visão geral das queries
- Informações do sistema e configurações

### Páginas Especializadas
- **Domínios**: Top domínios consultados e bloqueados
- **Clientes**: Dispositivos da rede e suas atividades
- **Logs**: Histórico detalhado de queries (em desenvolvimento)
- **Configurações**: Ajustes do dashboard (em desenvolvimento)

### Recursos Técnicos
- Sistema de cache configurável
- API RESTful para integração
- Tratamento de erros robusto
- Código limpo e bem documentado

## 🛠️ Instalação

### Pré-requisitos
- Servidor web com PHP 7.4+ (Apache/Nginx)
- Pi-hole funcionando na rede (IP: ************)
- Extensões PHP: `json`, `curl`

### Passos de Instalação

1. **Clone ou baixe o projeto**
   ```bash
   git clone [url-do-repositorio]
   cd DashboardMegainffo
   ```

2. **Configure o servidor web**
   - Aponte o DocumentRoot para a pasta do projeto
   - Certifique-se que o PHP está habilitado

3. **Configure o Pi-hole**
   - Edite o arquivo `config/config.php`
   - Ajuste o IP do Pi-hole se necessário:
   ```php
   define('PIHOLE_IP', '************');
   ```

4. **Configurações opcionais**
   ```php
   // Habilitar autenticação (opcional)
   define('ENABLE_AUTH', true);
   define('ADMIN_PASSWORD', 'sua_senha_aqui');
   
   // Ajustar cache
   define('CACHE_DURATION', 60); // segundos
   
   // Intervalo de atualização
   define('REFRESH_INTERVAL', 30); // segundos
   ```

5. **Teste a instalação**
   - Acesse `http://seu-servidor/` no navegador
   - Verifique se as estatísticas estão carregando

## 🔧 Configuração

### Estrutura de Arquivos
```
DashboardMegainffo/
├── config/
│   └── config.php          # Configurações principais
├── classes/
│   └── PiholeAPI.php       # Classe para comunicação com Pi-hole
├── api/
│   ├── get_stats.php       # Endpoint para estatísticas
│   ├── get_charts.php      # Endpoint para gráficos
│   └── toggle_pihole.php   # Endpoint para controles
├── assets/
│   ├── css/
│   │   └── style.css       # Estilos principais
│   └── js/
│       └── dashboard.js    # JavaScript do dashboard
├── pages/
│   ├── domains.php         # Página de domínios
│   ├── clients.php         # Página de clientes
│   ├── logs.php           # Página de logs (em desenvolvimento)
│   └── settings.php       # Página de configurações (em desenvolvimento)
├── index.php              # Página principal
└── README.md              # Este arquivo
```

### Configurações Avançadas

#### Cache
O sistema de cache pode ser configurado no arquivo `config/config.php`:
```php
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 60); // segundos
```

#### Autenticação
Para habilitar autenticação básica:
```php
define('ENABLE_AUTH', true);
define('ADMIN_PASSWORD', 'sua_senha_segura');
```

#### Personalização Visual
As cores e estilos podem ser ajustados no arquivo `assets/css/style.css` nas variáveis CSS:
```css
:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #06b6d4;
    /* ... outras variáveis */
}
```

## 🎨 Design

O dashboard foi inspirado no design da Megainffo, incorporando:
- **Paleta de cores**: Azuis profissionais com acentos em ciano
- **Tipografia**: Segoe UI para legibilidade moderna
- **Layout**: Grid responsivo com cards e gradientes
- **Animações**: Transições suaves e efeitos hover
- **Iconografia**: Emojis e símbolos para melhor UX

## 📱 Responsividade

O dashboard é totalmente responsivo e se adapta a:
- **Desktop**: Layout completo com sidebar e múltiplas colunas
- **Tablet**: Layout adaptado com navegação otimizada
- **Mobile**: Interface compacta com navegação em menu

## 🔌 API

### Endpoints Disponíveis

#### GET `/api/get_stats.php`
Retorna estatísticas gerais do Pi-hole
```json
{
    "success": true,
    "data": {
        "dns_queries_today": 1234,
        "ads_blocked_today": 567,
        "ads_percentage_today": 45.67,
        "unique_domains": 890,
        "unique_clients": 12,
        "status": "enabled"
    }
}
```

#### GET `/api/get_charts.php?type=overview`
Retorna dados para gráficos
- `type=overview`: Gráfico de pizza geral
- `type=history`: Histórico de queries
- `type=clients`: Top clientes
- `type=domains`: Top domínios

#### POST `/api/toggle_pihole.php`
Controla o estado do Pi-hole
```json
{
    "action": "enable|disable",
    "duration": 300,
    "password": "senha_se_habilitada"
}
```

## 🚨 Solução de Problemas

### Erro de Conexão com Pi-hole
1. Verifique se o IP está correto em `config/config.php`
2. Teste a conectividade: `ping ************`
3. Verifique se a API do Pi-hole está habilitada

### Dados não Carregam
1. Verifique os logs do servidor web
2. Teste os endpoints da API diretamente
3. Verifique permissões de arquivo

### Performance Lenta
1. Habilite o cache em `config/config.php`
2. Ajuste o `CACHE_DURATION`
3. Otimize o servidor web

## 🔒 Segurança

- Use HTTPS em produção
- Configure autenticação se necessário
- Mantenha o Pi-hole atualizado
- Restrinja acesso por IP se possível

## 📈 Próximas Funcionalidades

- [ ] Página de logs detalhados
- [ ] Configurações avançadas
- [ ] Notificações push
- [ ] Exportação de relatórios
- [ ] Modo escuro
- [ ] Múltiplos Pi-holes

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## 🙏 Agradecimentos

- **Pi-hole Team**: Pela excelente ferramenta de bloqueio de ads
- **Megainffo**: Pela inspiração do design
- **Chart.js**: Pelos gráficos interativos
- **Comunidade PHP**: Pelas melhores práticas

---

**Desenvolvido com ❤️ para a comunidade Pi-hole**
