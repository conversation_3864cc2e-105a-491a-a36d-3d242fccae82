/* Dashboard Zabbix - E<PERSON>ilo <PERSON> */

:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* Header */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo::before {
    content: "📊";
    font-size: 2rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.nav a:hover,
.nav a.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Container principal */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Grid de servidores */
.servers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Card de servidor */
.server-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.server-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.server-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.server-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-color);
}

.server-status {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-ok {
    background: #ecfdf5;
    color: var(--success-color);
}

.status-warning {
    background: #fef3c7;
    color: var(--warning-color);
}

.status-critical {
    background: #fef2f2;
    color: var(--danger-color);
}

.status-unknown {
    background: #f3f4f6;
    color: var(--text-secondary);
}

/* Métricas do servidor */
.server-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.metric {
    text-align: center;
}

.metric-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.metric-bar {
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.metric-progress {
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.metric-progress.cpu {
    background: linear-gradient(90deg, var(--success-color), var(--danger-color));
}

.metric-progress.memory {
    background: linear-gradient(90deg, var(--success-color), var(--warning-color));
}

.metric-progress.disk {
    background: linear-gradient(90deg, var(--success-color), var(--danger-color));
}

.metric-info {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Resumo geral */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.summary-card.servers::before {
    background: var(--primary-color);
}

.summary-card.problems::before {
    background: var(--danger-color);
}

.summary-card.warnings::before {
    background: var(--warning-color);
}

.summary-card.ok::before {
    background: var(--success-color);
}

.summary-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.summary-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.summary-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Tabela de problemas */
.problems-table {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.problems-table h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--text-primary);
}

.table tr:hover {
    background: var(--light-color);
}

.severity-high {
    color: var(--danger-color);
    font-weight: bold;
}

.severity-warning {
    color: var(--warning-color);
    font-weight: bold;
}

.severity-info {
    color: var(--secondary-color);
}

/* Gráficos */
.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-color);
}

.chart-wrapper {
    height: 300px;
    position: relative;
}

/* Botões */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsivo */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .container {
        padding: 1rem;
    }
    
    .servers-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .server-metrics {
        grid-template-columns: 1fr;
    }
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Utilitários */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
