<?php
/**
 * Classe para comunicação com a API do Pi-hole
 * Desenvolvida para o Dashboard Megainffo Style
 */

class PiholeAPI {
    private $apiUrl;
    private $apiKey;
    private $timeout;
    private $cache;
    private $cacheEnabled;
    private $cacheDuration;

    public function __construct($apiUrl = null, $apiKey = null) {
        $this->apiUrl = $apiUrl ?: PIHOLE_API_URL;
        $this->apiKey = $apiKey ?: (defined('PIHOLE_API_TOKEN') ? PIHOLE_API_TOKEN : null);
        $this->timeout = 10;
        $this->cache = [];
        $this->cacheEnabled = CACHE_ENABLED;
        $this->cacheDuration = CACHE_DURATION;
    }

    /**
     * Fazer requisição para a API do Pi-hole
     */
    private function makeRequest($endpoint, $params = []) {
        $cacheKey = md5($endpoint . serialize($params));
        
        // Verificar cache
        if ($this->cacheEnabled && isset($this->cache[$cacheKey])) {
            $cached = $this->cache[$cacheKey];
            if (time() - $cached['timestamp'] < $this->cacheDuration) {
                return $cached['data'];
            }
        }

        $url = $this->apiUrl;
        
        // Adicionar parâmetros
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        } else if ($endpoint) {
            $url .= '?' . $endpoint;
        }

        // Configurar contexto da requisição
        $context = stream_context_create([
            'http' => [
                'timeout' => $this->timeout,
                'method' => 'GET',
                'header' => [
                    'User-Agent: PiholeDashboard/1.0',
                    'Accept: application/json'
                ]
            ]
        ]);

        // Fazer requisição
        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('Erro ao conectar com o Pi-hole em ' . PIHOLE_IP);
        }

        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Resposta inválida da API do Pi-hole');
        }

        // Salvar no cache
        if ($this->cacheEnabled) {
            $this->cache[$cacheKey] = [
                'data' => $data,
                'timestamp' => time()
            ];
        }

        return $data;
    }

    /**
     * Obter estatísticas gerais do Pi-hole
     */
    public function getStats() {
        try {
            $data = $this->makeRequest('summary');
            
            return [
                'success' => true,
                'data' => [
                    'dns_queries_today' => (int)$data['dns_queries_today'],
                    'ads_blocked_today' => (int)$data['ads_blocked_today'],
                    'ads_percentage_today' => round((float)$data['ads_percentage_today'], 2),
                    'unique_domains' => (int)$data['unique_domains'],
                    'unique_clients' => (int)$data['unique_clients'],
                    'queries_forwarded' => (int)$data['queries_forwarded'],
                    'queries_cached' => (int)$data['queries_cached'],
                    'clients_ever_seen' => (int)$data['clients_ever_seen'],
                    'status' => $data['status'],
                    'gravity_last_updated' => $data['gravity_last_updated'] ?? null
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter histórico de queries por hora
     */
    public function getQueryHistory() {
        try {
            $data = $this->makeRequest('overTimeDataClients');
            
            return [
                'success' => true,
                'data' => $data
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter top domains
     */
    public function getTopDomains($limit = 10) {
        try {
            $data = $this->makeRequest('topItems', ['limit' => $limit]);
            
            return [
                'success' => true,
                'data' => [
                    'top_queries' => $data['top_queries'] ?? [],
                    'top_ads' => $data['top_ads'] ?? []
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter top clients
     */
    public function getTopClients($limit = 10) {
        try {
            $data = $this->makeRequest('topClients', ['limit' => $limit]);
            
            return [
                'success' => true,
                'data' => $data
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Obter tipos de queries
     */
    public function getQueryTypes() {
        try {
            $data = $this->makeRequest('queryTypesOverTime');
            
            return [
                'success' => true,
                'data' => $data
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Habilitar Pi-hole
     */
    public function enable() {
        try {
            if (!$this->apiKey) {
                throw new Exception('API Token necessário para operações administrativas');
            }

            $params = ['enable' => '', 'auth' => $this->apiKey];
            $data = $this->makeRequest('', $params);

            return [
                'success' => isset($data['status']) && $data['status'] === 'enabled',
                'message' => isset($data['status']) ? 'Pi-hole habilitado com sucesso' : 'Erro desconhecido',
                'status' => $data['status'] ?? 'unknown'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Desabilitar Pi-hole
     */
    public function disable($duration = 0) {
        try {
            if (!$this->apiKey) {
                throw new Exception('API Token necessário para operações administrativas');
            }

            $params = ['disable' => $duration, 'auth' => $this->apiKey];
            $data = $this->makeRequest('', $params);

            return [
                'success' => isset($data['status']) && $data['status'] === 'disabled',
                'message' => isset($data['status']) ? 'Pi-hole desabilitado com sucesso' : 'Erro desconhecido',
                'status' => $data['status'] ?? 'unknown'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Verificar status da conexão
     */
    public function testConnection() {
        try {
            $data = $this->makeRequest('version');
            
            return [
                'success' => true,
                'data' => [
                    'version' => $data['version'] ?? 'Desconhecida',
                    'connection' => 'OK'
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Limpar cache
     */
    public function clearCache() {
        $this->cache = [];
        return true;
    }

    /**
     * Definir API Key
     */
    public function setApiKey($apiKey) {
        $this->apiKey = $apiKey;
    }

    /**
     * Obter informações do sistema
     */
    public function getSystemInfo() {
        try {
            $stats = $this->getStats();
            $version = $this->testConnection();
            
            if (!$stats['success'] || !$version['success']) {
                throw new Exception('Erro ao obter informações do sistema');
            }
            
            return [
                'success' => true,
                'data' => [
                    'pihole_version' => $version['data']['version'],
                    'status' => $stats['data']['status'],
                    'uptime' => $this->getUptime(),
                    'last_update' => date('Y-m-d H:i:s')
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Calcular uptime (simulado)
     */
    private function getUptime() {
        // Em uma implementação real, você obteria isso do sistema
        return time() - strtotime('today');
    }
}
?>
