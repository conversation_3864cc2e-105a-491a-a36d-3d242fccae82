<?php
/**
 * Dashboard Zabbix - Página de Hosts/Servidores
 */

require_once '../config/config.php';
require_once '../classes/ZabbixAPI.php';

$zabbix = new ZabbixAPI();
$hosts = $zabbix->getHosts();
$groups = $zabbix->getHostGroups();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Servidores - <?php echo ZABBIX_DASHBOARD_TITLE; ?></title>
    <link rel="stylesheet" href="../assets/css/zabbix-style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                Dashboard Zabbix
                <span style="font-size: 0.8rem; opacity: 0.8;">Megainffo Style</span>
            </div>
            <nav class="nav">
                <a href="../index.php">Dashboard</a>
                <a href="#" class="active">Servidores</a>
                <a href="problems.php">Problemas</a>
                <a href="charts.php">Gráficos</a>
                <a href="settings.php">Configurações</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="page-header mb-3">
            <h1>🖥️ Servidores Monitorados</h1>
            <p>Visão detalhada de todos os servidores e suas métricas</p>
        </div>

        <!-- Filtros e Controles -->
        <div class="controls-section mb-3">
            <div style="background: white; padding: 1.5rem; border-radius: 1rem; box-shadow: var(--shadow);">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                    <div style="display: flex; gap: 1rem; align-items: center;">
                        <label>Grupo:</label>
                        <select id="group-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem;">
                            <option value="">Todos os grupos</option>
                            <?php if ($groups['success']): ?>
                                <?php foreach ($groups['data'] as $group): ?>
                                    <option value="<?php echo $group['groupid']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        
                        <label>Status:</label>
                        <select id="status-filter" style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 0.25rem;">
                            <option value="">Todos</option>
                            <option value="1">Online</option>
                            <option value="2">Offline</option>
                            <option value="0">Desconhecido</option>
                        </select>
                    </div>
                    
                    <div style="display: flex; gap: 0.5rem;">
                        <button id="refresh-btn" class="btn btn-primary">🔄 Atualizar</button>
                        <button id="toggle-view" class="btn btn-primary">📋 Visualização</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estatísticas Rápidas -->
        <div class="summary-grid mb-3">
            <div class="summary-card servers">
                <div class="summary-icon">🖥️</div>
                <div class="summary-value" id="total-hosts">-</div>
                <div class="summary-label">Total de Servidores</div>
            </div>
            
            <div class="summary-card ok">
                <div class="summary-icon">✅</div>
                <div class="summary-value" id="hosts-online">-</div>
                <div class="summary-label">Online</div>
            </div>
            
            <div class="summary-card problems">
                <div class="summary-icon">❌</div>
                <div class="summary-value" id="hosts-offline">-</div>
                <div class="summary-label">Offline</div>
            </div>
            
            <div class="summary-card warnings">
                <div class="summary-icon">❓</div>
                <div class="summary-value" id="hosts-unknown">-</div>
                <div class="summary-label">Desconhecido</div>
            </div>
        </div>

        <!-- Lista de Servidores -->
        <div id="hosts-container">
            <!-- Visualização em Cards (padrão) -->
            <div id="card-view" class="servers-grid">
                <!-- Será preenchido via JavaScript -->
            </div>

            <!-- Visualização em Tabela -->
            <div id="table-view" style="display: none;">
                <div style="background: white; border-radius: 1rem; padding: 1.5rem; box-shadow: var(--shadow);">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Servidor</th>
                                <th>Status</th>
                                <th>IP</th>
                                <th>Grupos</th>
                                <th>CPU</th>
                                <th>Memória</th>
                                <th>Disco</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="hosts-table-body">
                            <!-- Será preenchido via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Loading -->
        <div id="loading" style="text-align: center; padding: 2rem; display: none;">
            <div class="loading" style="margin: 0 auto;"></div>
            <p>Carregando servidores...</p>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../assets/js/zabbix-dashboard.js"></script>
    
    <script>
        class HostsPage {
            constructor() {
                this.currentView = 'card';
                this.hosts = [];
                this.filteredHosts = [];
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadHosts();
            }

            setupEventListeners() {
                // Filtros
                document.getElementById('group-filter').addEventListener('change', () => this.applyFilters());
                document.getElementById('status-filter').addEventListener('change', () => this.applyFilters());
                
                // Botões
                document.getElementById('refresh-btn').addEventListener('click', () => this.loadHosts());
                document.getElementById('toggle-view').addEventListener('click', () => this.toggleView());
            }

            async loadHosts() {
                try {
                    this.showLoading();
                    
                    const response = await fetch('../api/get_hosts.php?include_metrics=true');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.hosts = data.data;
                        this.updateStats();
                        this.applyFilters();
                    } else {
                        throw new Error(data.message);
                    }
                } catch (error) {
                    console.error('Erro ao carregar hosts:', error);
                    this.showError('Erro ao carregar servidores: ' + error.message);
                } finally {
                    this.hideLoading();
                }
            }

            updateStats() {
                const stats = {
                    total: this.hosts.length,
                    online: this.hosts.filter(h => h.available === '1').length,
                    offline: this.hosts.filter(h => h.available === '2').length,
                    unknown: this.hosts.filter(h => h.available === '0').length
                };

                document.getElementById('total-hosts').textContent = stats.total;
                document.getElementById('hosts-online').textContent = stats.online;
                document.getElementById('hosts-offline').textContent = stats.offline;
                document.getElementById('hosts-unknown').textContent = stats.unknown;
            }

            applyFilters() {
                const groupFilter = document.getElementById('group-filter').value;
                const statusFilter = document.getElementById('status-filter').value;

                this.filteredHosts = this.hosts.filter(host => {
                    let matchGroup = true;
                    let matchStatus = true;

                    if (groupFilter) {
                        matchGroup = host.groups.some(group => group.groupid === groupFilter);
                    }

                    if (statusFilter) {
                        matchStatus = host.available === statusFilter;
                    }

                    return matchGroup && matchStatus;
                });

                this.renderHosts();
            }

            renderHosts() {
                if (this.currentView === 'card') {
                    this.renderCardView();
                } else {
                    this.renderTableView();
                }
            }

            renderCardView() {
                const container = document.getElementById('card-view');
                
                if (this.filteredHosts.length === 0) {
                    container.innerHTML = `
                        <div class="server-card">
                            <div style="text-align: center; padding: 2rem;">
                                <p>Nenhum servidor encontrado com os filtros aplicados</p>
                            </div>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = this.filteredHosts.map(host => this.createHostCard(host)).join('');
            }

            renderTableView() {
                const tbody = document.getElementById('hosts-table-body');
                
                if (this.filteredHosts.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 2rem;">
                                Nenhum servidor encontrado com os filtros aplicados
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = this.filteredHosts.map(host => this.createHostRow(host)).join('');
            }

            createHostCard(host) {
                const metrics = host.metrics || {};
                const cpu = metrics.cpu || { usage: 0, status: 'unknown' };
                const memory = metrics.memory || { usage: 0, status: 'unknown' };
                const disk = metrics.disk || { usage: 0, status: 'unknown' };

                return `
                    <div class="server-card fade-in-up" data-hostid="${host.hostid}">
                        <div class="server-header">
                            <div class="server-name" title="${host.hostname}">${host.name}</div>
                            <div class="server-status status-${host.status_class}">${host.status_text}</div>
                        </div>
                        <div class="server-info mb-2">
                            <small><strong>IP:</strong> ${host.interfaces?.[0]?.ip || 'N/A'}</small><br>
                            <small><strong>Grupos:</strong> ${host.groups.map(g => g.name).join(', ')}</small>
                        </div>
                        <div class="server-metrics">
                            <div class="metric">
                                <div class="metric-label">CPU</div>
                                <div class="metric-value">${cpu.usage.toFixed(1)}%</div>
                                <div class="metric-bar">
                                    <div class="metric-progress cpu" style="width: ${cpu.usage}%"></div>
                                </div>
                                <div class="metric-info">${this.getStatusText(cpu.status)}</div>
                            </div>
                            <div class="metric">
                                <div class="metric-label">Memória</div>
                                <div class="metric-value">${memory.usage.toFixed(1)}%</div>
                                <div class="metric-bar">
                                    <div class="metric-progress memory" style="width: ${memory.usage}%"></div>
                                </div>
                                <div class="metric-info">${this.getStatusText(memory.status)}</div>
                            </div>
                            <div class="metric">
                                <div class="metric-label">Disco</div>
                                <div class="metric-value">${disk.usage.toFixed(1)}%</div>
                                <div class="metric-bar">
                                    <div class="metric-progress disk" style="width: ${disk.usage}%"></div>
                                </div>
                                <div class="metric-info">${this.getStatusText(disk.status)}</div>
                            </div>
                            <div class="metric">
                                <div class="metric-label">Ações</div>
                                <button class="btn btn-primary" onclick="viewHostDetails('${host.hostid}')">
                                    📊 Detalhes
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            createHostRow(host) {
                const metrics = host.metrics || {};
                const cpu = metrics.cpu || { usage: 0 };
                const memory = metrics.memory || { usage: 0 };
                const disk = metrics.disk || { usage: 0 };

                return `
                    <tr data-hostid="${host.hostid}">
                        <td>
                            <strong>${host.name}</strong><br>
                            <small>${host.hostname}</small>
                        </td>
                        <td>
                            <span class="server-status status-${host.status_class}">${host.status_text}</span>
                        </td>
                        <td>${host.interfaces?.[0]?.ip || 'N/A'}</td>
                        <td>${host.groups.map(g => g.name).join(', ')}</td>
                        <td>${cpu.usage.toFixed(1)}%</td>
                        <td>${memory.usage.toFixed(1)}%</td>
                        <td>${disk.usage.toFixed(1)}%</td>
                        <td>
                            <button class="btn btn-primary" onclick="viewHostDetails('${host.hostid}')">
                                📊 Detalhes
                            </button>
                        </td>
                    </tr>
                `;
            }

            toggleView() {
                const cardView = document.getElementById('card-view');
                const tableView = document.getElementById('table-view');
                const toggleBtn = document.getElementById('toggle-view');

                if (this.currentView === 'card') {
                    cardView.style.display = 'none';
                    tableView.style.display = 'block';
                    toggleBtn.textContent = '📋 Cards';
                    this.currentView = 'table';
                } else {
                    cardView.style.display = 'grid';
                    tableView.style.display = 'none';
                    toggleBtn.textContent = '📋 Tabela';
                    this.currentView = 'card';
                }

                this.renderHosts();
            }

            getStatusText(status) {
                switch (status) {
                    case 'ok': return 'Normal';
                    case 'warning': return 'Atenção';
                    case 'critical': return 'Crítico';
                    default: return 'Desconhecido';
                }
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('hosts-container').style.display = 'none';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('hosts-container').style.display = 'block';
            }

            showError(message) {
                // Implementar notificação de erro
                alert(message);
            }
        }

        // Função global para ver detalhes do host
        function viewHostDetails(hostId) {
            window.open(`host_details.php?hostid=${hostId}`, '_blank');
        }

        // Inicializar página
        document.addEventListener('DOMContentLoaded', function() {
            window.hostsPage = new HostsPage();
        });
    </script>

    <style>
        .page-header h1 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .page-header p {
            color: var(--text-secondary);
            margin: 0;
        }
        
        .controls-section label {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .server-info {
            font-size: 0.85rem;
            color: var(--text-secondary);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .controls-section > div {
                flex-direction: column;
                align-items: stretch;
            }
            
            .controls-section > div > div {
                justify-content: center;
            }
        }
    </style>
</body>
</html>
