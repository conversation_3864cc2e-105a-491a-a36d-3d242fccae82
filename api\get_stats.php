<?php
/**
 * API Endpoint - Obter estatísticas do Pi-hole
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir configurações e classes
require_once '../config/config.php';
require_once '../classes/PiholeAPI.php';

try {
    // Criar instância da API
    $pihole = new PiholeAPI();
    
    // Obter estatísticas
    $stats = $pihole->getStats();
    
    if ($stats['success']) {
        // Adicionar informações extras
        $stats['data']['last_update'] = date('Y-m-d H:i:s');
        $stats['data']['server_time'] = time();
        
        // Calcular estatísticas adicionais
        $total_queries = $stats['data']['dns_queries_today'];
        $blocked_queries = $stats['data']['ads_blocked_today'];
        
        if ($total_queries > 0) {
            $stats['data']['allowed_queries'] = $total_queries - $blocked_queries;
            $stats['data']['block_ratio'] = round($blocked_queries / $total_queries, 4);
        } else {
            $stats['data']['allowed_queries'] = 0;
            $stats['data']['block_ratio'] = 0;
        }
        
        // Adicionar status de saúde
        $stats['data']['health_status'] = $stats['data']['status'] === 'enabled' ? 'healthy' : 'warning';
        
        echo json_encode($stats);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $stats['message'],
            'error_code' => 'API_ERROR'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro interno do servidor: ' . $e->getMessage(),
        'error_code' => 'INTERNAL_ERROR'
    ]);
}
?>
